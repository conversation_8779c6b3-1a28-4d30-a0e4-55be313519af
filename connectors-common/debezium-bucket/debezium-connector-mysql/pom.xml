<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>io.debezium</groupId>
        <artifactId>debezium-parent</artifactId>
        <version>1.5.4.Final</version>
	<relativePath>../debezium-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>debezium-connector-mysql</artifactId>
    <name>Debezium Connector for MySQL</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-ddl-parser</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tapdata</groupId>
            <artifactId>mysql-binlog-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>mil.nga</groupId>
            <artifactId>wkb</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-core</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-embedded</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.debezium</groupId>
            <artifactId>debezium-embedded</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.easytesting</groupId>
            <artifactId>fest-assert</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-connect-avro-converter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Used for unit testing with Kafka -->
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_${version.kafka.scala}</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <!-- 
        Specify the properties for the various Docker containers.
        -->
        <mysql.user>mysqluser</mysql.user>
        <mysql.password>mysqlpw</mysql.password>
        <mysql.replica.user>mysqlreplica</mysql.replica.user>
        <mysql.replica.password>mysqlpw</mysql.replica.password>
        <mysql.port>3306</mysql.port>
        <mysql.percona.port>3306</mysql.percona.port>
        <mysql.gtid.port>3306</mysql.gtid.port>
        <mysql.gtid.replica.port>3306</mysql.gtid.replica.port>
        <mysql.replica.port>3306</mysql.replica.port> <!-- by default use primary as 'replica' -->
        <mysql.init.timeout>60000</mysql.init.timeout> <!-- 60 seconds -->
        <!--
        By default, we should use the docker image maintained by the MySQL team. This property is changed with different profiles.
        However, we run one container with GTIDs and one without.
        -->
        <docker.filter>debezium/mysql-server-test-database</docker.filter>
        <docker.skip>false</docker.skip>
        <docker.initimage>ln -s /usr/share/zoneinfo/US/Samoa /etc/localtime</docker.initimage>
    </properties>
    <build>
        <plugins>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-assembly-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <!-- Apply the properties set in the POM to the resource files -->
            <resource>
                <filtering>true</filtering>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*</include>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>*</include>
                    <include>**/*</include>
                </includes>
            </testResource>
        </testResources>
    </build>
</project>
