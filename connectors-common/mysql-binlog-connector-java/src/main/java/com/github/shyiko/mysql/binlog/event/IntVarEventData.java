/*
 * Copyright 2015 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.github.shyiko.mysql.binlog.event;

/**
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON></a>
 */
public class IntVarEventData implements EventData {

    /**
     * Type indicating whether the value is meant to be used for the LAST_INSERT_ID() invocation (should be equal 1) or
     * AUTO_INCREMENT column (should be equal 2).
     */
    private int type;
    private long value;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        sb.append("IntVarEventData");
        sb.append("{type=").append(type);
        sb.append(", value=").append(value);
        sb.append('}');
        return sb.toString();
    }

}
