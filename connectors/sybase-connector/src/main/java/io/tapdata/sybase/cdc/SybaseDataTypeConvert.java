package io.tapdata.sybase.cdc;

import io.tapdata.entity.error.CoreException;
import io.tapdata.sybase.util.Utils;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

public class SybaseDataTypeConvert {

    public static Object objToTimestamp(Object obj, String type) {
        if (null == obj) return null;
        if (obj instanceof String) {
            return LocalDateTime.parse(((String) obj).replace(" ", "T"));
        } else if (obj instanceof Number) {
            try {
                return new Timestamp(((Number) obj).longValue());
            } catch (Exception e) {
                throw new IllegalArgumentException("Error convert to " + type);
            }
        } else {
            throw new IllegalArgumentException("Error convert to " + type);
        }
    }

    public static Map<String, Object> filterTimeForDataBase(
            ResultSet resultSet,
            Map<String, String> typeAndName,
            Set<String> dateTypeSet,
            boolean needEncode,
            String encode,
            String decode
    ) {
        Map<String, Object> data = new HashMap<>();
        for (Map.Entry<String, String> entry : typeAndName.entrySet()) {
            String metaType = entry.getValue();
            String metaName = entry.getKey();
            try {
                switch (metaType) {
                    case "TIME":
                    case "DATE":
                    case "BIGTIME":
                        data.put(metaName, resultSet.getString(metaName));
                        break;
                    case "DATETIME":
                        data.put(metaName, objToTimestamp(resultSet.getString(metaName), "DATETIME"));
                        break;
                    default:
                        if (needEncode && (metaType.contains("CHAR")
                                || metaType.contains("SYSNAME"))) {
                            String string = resultSet.getString(metaName);
                            data.put(metaName, Utils.convertString(string, encode, decode));
                        } else if (metaType.contains("DATETIME")) {
                            data.put(metaName, SybaseDataTypeConvert.objToTimestamp(resultSet.getString(metaName), "DATETIME"));
                        } else {
                            Object value = resultSet.getObject(metaName);
                            if (null == value && dateTypeSet.contains(metaName)) {
                                value = resultSet.getString(metaName);
                            }
                            data.put(metaName, value);
                        }
                }
            } catch (Exception e) {
                throw new CoreException("Read column value failed, column name: {}, type: {}, data: {}, error: {}", metaName, metaType, data, e.getMessage());
            }
        }
        return data;
    }

    public static Map<String, String> getTypeAndName(ResultSet resultSet) throws SQLException {
        ResultSetMetaData metaData = resultSet.getMetaData();
        Map<String, String> typeAndNameFromMetaData = new HashMap<>();
        int columnCount = metaData.getColumnCount();
        for (int index = 1; index < columnCount + 1; index++) {
            String type = metaData.getColumnTypeName(index);
            if (null == type) continue;
            typeAndNameFromMetaData.put(metaData.getColumnName(index), type.toUpperCase(Locale.ROOT));
        }
        return typeAndNameFromMetaData;
    }
}
