<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<groupId>io.tapdata</groupId>
	<modelVersion>4.0.0</modelVersion>
    <artifactId>connectors</artifactId>
    <name>connectors</name>
	<version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
<!--		<module>tdd-connector</module>-->
        <module>dummy-connector</module>
        <module>mongodb-connector</module>
		<module>mongodb-atlas-connector</module>
		<module>mysql-connector</module>
        <module>postgres-connector</module>
		<module>elasticsearch-connector</module>
        <module>doris-connector</module>
        <module>starrocks-connector</module>
        <module>oceanbase-mysql-connector</module>
		<module>kafka-connector</module>
		<module>kafka-enhanced-connector</module>
        <module>rabbitmq-connector</module>
        <module>rocketmq-connector</module>
		<module>activemq-connector</module>
        <module>redis-connector</module>
		<module>clickhouse-connector</module>
		<module>mariadb-connector</module>
		<module>coding-connector</module>
		<module>zoho-desk-connector</module>
		<module>tidb-connector</module>
		<module>tablestore-connector</module>
		<module>custom-connector</module>
		<module>bigquery-connector</module>
		<module>excel-connector</module>
		<module>csv-connector</module>
		<module>xml-connector</module>
		<module>json-connector</module>
		<module>tdengine-connector</module>
		<module>vika-connector</module>
		<module>selectdb-connector</module>
		<module>quickapi-connector</module>
		<module>aliyun-adb-mysql-connector</module>
		<module>aliyun-rds-mysql-connector</module>
		<module>mysql-pxc-connector</module>
		<module>polar-db-mysql-connector</module>
		<module>tencent-db-mongodb-connector</module>
		<module>aliyun-mongodb-connector</module>
		<module>aliyun-adb-postgres-connector</module>
		<module>tencent-db-postgres-connector</module>
		<module>aliyun-rds-postgres-connector</module>
		<module>polar-db-postgres-connector</module>
		<module>aws-rds-mysql-connector</module>
		<module>tencent-db-mariadb-connector</module>
		<module>aliyun-rds-mariadb-connector</module>
		<module>databend-connector</module>
		<module>hazelcast-connector</module>
		<module>opengauss-connector</module>
		<module>http-receiver-connector</module>
		<module>yashandb-connector</module>
		<module>greenplum-connector</module>
		<module>dws-connector</module>
		<module>azure-cosmosdb-connector</module>
        <module>hudi-connector</module>
		<module>huawei-cloud-gaussdb-connector</module>
		<module>aws-clickhouse-connector</module>
		<module>mongodb-lower-connector</module>
        <module>mock-source-connector</module>
		<module>mock-target-connector</module>
		<module>vastbase-connector</module>
		<module>highgo-connector</module>
		<module>file-stream-connector</module>
	</modules>
	<properties>
		<connector.file.name>${project.artifactId}-v${project.version}</connector.file.name>
		<java.version>8</java.version>
		<tapdata.pdk.runner.version>2.0-SNAPSHOT</tapdata.pdk.runner.version>
		<tapdata.pdk.api.version>2.0.0-SNAPSHOT</tapdata.pdk.api.version>
		<tapdata.pdk.connector.core.version>1.0-SNAPSHOT</tapdata.pdk.connector.core.version>
		<junit.jupiter.version>5.8.1</junit.jupiter.version>
		<junit.platform.version>1.8.1</junit.platform.version>
		<commons.lang3.version>3.12.0</commons.lang3.version>
		<commons.collections4.version>4.4</commons.collections4.version>
		<log4j.version>2.17.1</log4j.version>
		<guava.version>31.0.1-jre</guava.version>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<pdk-error-code.version>1.0-SNAPSHOT</pdk-error-code.version>
		<mockito.version>4.11.0</mockito.version>
		<byte-buddy.version>1.12.23</byte-buddy.version>
		<pay.mode>Free</pay.mode>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>5.0.4.RELEASE</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-api</artifactId>
				<version>${tapdata.pdk.api.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-api</artifactId>
				<version>${tapdata.pdk.api.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-runner</artifactId>
				<version>${tapdata.pdk.runner.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>connector-core</artifactId>
				<version>${tapdata.pdk.connector.core.version}</version>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-engine</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-api</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-params</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-suite</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-launcher</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>${commons.lang3.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>${commons.collections4.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>pdk-error-code</artifactId>
				<version>${pdk-error-code.version}</version>
				<scope>provided</scope>
			</dependency>

			<!-- Mockito -->
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>${mockito.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-junit-jupiter</artifactId>
				<version>${mockito.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-inline</artifactId>
				<version>${mockito.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>net.bytebuddy</groupId>
				<artifactId>byte-buddy</artifactId>
				<version>${byte-buddy.version}</version>
			</dependency>
			<dependency>
				<groupId>net.bytebuddy</groupId>
				<artifactId>byte-buddy-agent</artifactId>
				<version>${byte-buddy.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>

		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>2.22.1</version>
				</plugin>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>0.8.6</version>
				</plugin>
			</plugins>
		</pluginManagement>
    </build>
	<repositories>
		<repository>
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
</project>

