package io.tapdata.connector.db2.config;

import io.tapdata.common.CommonDbConfig;

public class Db2Config extends CommonDbConfig {

    private String rawLogServerHost;
    private int rawLogServerPort;
    private Boolean autoLri = true;
    private int sshPort = 22;
    private String db2Profile;
    private Boolean archived = false;
    private String archiveDir;
    private String sqlCtlDir;
    private String initialLri;

    public Db2Config() {
        setDbType("db2");
        setJdbcDriver("com.ibm.db2.jcc.DB2Driver");
    }

    public String getRawLogServerHost() {
        return rawLogServerHost;
    }

    public void setRawLogServerHost(String rawLogServerHost) {
        this.rawLogServerHost = rawLogServerHost;
    }

    public int getRawLogServerPort() {
        return rawLogServerPort;
    }

    public void setRawLogServerPort(int rawLogServerPort) {
        this.rawLogServerPort = rawLogServerPort;
    }

    public Boolean getAutoLri() {
        return autoLri;
    }

    public void setAutoLri(Boolean autoLri) {
        this.autoLri = autoLri;
    }

    public int getSshPort() {
        return sshPort;
    }

    public void setSshPort(int sshPort) {
        this.sshPort = sshPort;
    }

    public String getDb2Profile() {
        return db2Profile;
    }

    public void setDb2Profile(String db2Profile) {
        this.db2Profile = db2Profile;
    }

    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }

    public String getArchiveDir() {
        return archiveDir;
    }

    public void setArchiveDir(String archiveDir) {
        this.archiveDir = archiveDir;
    }

    public String getSqlCtlDir() {
        return sqlCtlDir;
    }

    public void setSqlCtlDir(String sqlCtlDir) {
        this.sqlCtlDir = sqlCtlDir;
    }

    public String getInitialLri() {
        return initialLri;
    }

    public void setInitialLri(String initialLri) {
        this.initialLri = initialLri;
    }
}
