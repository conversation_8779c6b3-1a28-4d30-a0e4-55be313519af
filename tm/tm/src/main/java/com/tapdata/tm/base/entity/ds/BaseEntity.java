package com.tapdata.tm.base.entity.ds;

import com.tapdata.tm.base.entity.Entity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/9/11 3:19 下午
 * @description
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class BaseEntity extends Entity {

	/**
	 * 租户ID
	 */
	@Indexed
	private String customId;

	@Indexed
	@Field("createTime")
	private String createAt;
	@Indexed
	@Field("last_updated")
	private Date lastUpdAt;
	@Indexed
	@Field("user_id")
	private String userId;
	@Indexed
	private String lastUpdBy;
	@Indexed
	private String createUser;
}
