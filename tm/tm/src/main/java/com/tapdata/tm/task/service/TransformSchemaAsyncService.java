package com.tapdata.tm.task.service;

import com.tapdata.tm.commons.dag.DAG;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.lock.annotation.Lock;
import com.tapdata.tm.lock.constant.LockType;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;


/**
 * @Author: Zed
 * @Date: 2021/12/17
 * @Description:
 */
@Service@Slf4j
public class TransformSchemaAsyncService {

    @Autowired
    private TransformSchemaService transformSchemaService;


    /**
     * 异步的模型推演，
     * @param dag
     * @param user
     * @param taskId
     */
    @Async
    @Lock(value = "taskId", type = LockType.TRANSFORM_SCHEMA)
    public void transformSchema(DAG dag, UserDetail user, ObjectId taskId) {
        transformSchemaService.transformSchema(dag, user, taskId);
    }
}
