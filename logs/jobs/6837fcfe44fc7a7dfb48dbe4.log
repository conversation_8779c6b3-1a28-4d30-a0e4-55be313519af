[TRACE] 2025-06-12 11:57:16.855 - [任务 4] - Task initialization... 
[TRACE] 2025-06-12 11:57:16.866 - [任务 4] - Start task milestones: 6837fcfe44fc7a7dfb48dbe4(任务 4) 
[INFO ] 2025-06-12 11:57:17.098 - [任务 4] - Loading table structure completed 
[TRACE] 2025-06-12 11:57:17.186 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-12 11:57:17.186 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-12 11:57:17.341 - [任务 4] - Task started 
[TRACE] 2025-06-12 11:57:17.341 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] start preload schema,table counts: 3 
[TRACE] 2025-06-12 11:57:17.341 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] start preload schema,table counts: 3 
[TRACE] 2025-06-12 11:57:17.341 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] preload schema finished, cost 1 ms 
[TRACE] 2025-06-12 11:57:17.342 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] preload schema finished, cost 0 ms 
[INFO ] 2025-06-12 11:57:17.491 - [任务 4][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-12 11:57:17.491 - [任务 4][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-12 11:57:17.492 - [任务 4][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-12 11:57:17.506 - [任务 4][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-12 11:57:17.705 - [任务 4][sqlserver -ag1] - Source connector(sqlserver -ag1) initialization completed 
[TRACE] 2025-06-12 11:57:17.707 - [任务 4][sqlserver -ag1] - Source node "sqlserver -ag1" read batch size: 100 
[TRACE] 2025-06-12 11:57:17.708 - [任务 4][sqlserver -ag1] - Source node "sqlserver -ag1" event queue capacity: 200 
[INFO ] 2025-06-12 11:57:17.708 - [任务 4][sqlserver -ag1] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-12 11:57:17.718 - [任务 4][sqlserver -ag1] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"},"SourceOfRegion3":{"batch_read_connector_status":"OVER"},"SourceOfRegion2":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"currentStartLSN":"0000003700005F900001","tablesOffset":{"SourceOfRegion":"0000003700005F900001","SourceOfRegion3":"0000003700005F900001","SourceOfRegion2":"0000003700005F900001"},"ddlOffset":"AAAANwAAX5AAAQ=="} 
[INFO ] 2025-06-12 11:57:17.775 - [任务 4][sqlserver -ag1] - Batch read completed. 
[TRACE] 2025-06-12 11:57:17.775 - [任务 4][sqlserver -ag1] - Incremental sync starting... 
[TRACE] 2025-06-12 11:57:17.775 - [任务 4][sqlserver -ag1] - Initial sync completed 
[TRACE] 2025-06-12 11:57:17.777 - [任务 4][sqlserver -ag1] - Starting stream read, table list: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], offset: {"currentStartLSN":"0000003700005F900001","tablesOffset":{"SourceOfRegion":"0000003700005F900001","SourceOfRegion3":"0000003700005F900001","SourceOfRegion2":"0000003700005F900001"},"ddlOffset":"AAAANwAAX5AAAQ=="} 
[INFO ] 2025-06-12 11:57:17.978 - [任务 4][sqlserver -ag1] - Starting incremental sync using database log parser 
[INFO ] 2025-06-12 11:57:18.183 - [任务 4][sqlserver -ag1] - opened cdc tables: [SourceOfRegion, SampleTable, SourceOfRegion3, SourceOfRegion2] 
[WARN ] 2025-06-12 11:57:18.385 - [任务 4][sqlserver -ag1] - Failed to obtain events through LSN.The log may have been cleared. Will get current LSN event. offsetLSN: 0000003700005F900001, currentLSN: 0000004A00003D900001 
[WARN ] 2025-06-12 11:57:18.791 - [任务 4][sqlserver -ag1] - Failed to obtain events through LSN.The log may have been cleared. Will get current LSN event. offsetLSN: 0000003700005F900001, currentLSN: 0000004A00003D900001 
[WARN ] 2025-06-12 11:57:19.196 - [任务 4][sqlserver -ag1] - Failed to obtain events through LSN.The log may have been cleared. Will get current LSN event. offsetLSN: 0000003700005F900001, currentLSN: 0000004A00003D900001 
[INFO ] 2025-06-12 11:57:19.265 - [任务 4][sqlserver -ag1] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-12 11:57:19.470 - [任务 4][sqlserver -ag1] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion, SourceOfRegion3, SourceOfRegion2], data change syncing 
[TRACE] 2025-06-12 11:57:58.098 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] running status set to false 
[TRACE] 2025-06-12 11:57:58.907 - [任务 4][sqlserver -ag1] - Incremental sync completed 
[TRACE] 2025-06-12 11:58:01.124 - [任务 4][sqlserver -ag1] - PDK connector node stopped: HazelcastSourcePdkDataNode_636436a7-938f-401c-b112-b9aede3e3e79_1749700637362 
[TRACE] 2025-06-12 11:58:01.125 - [任务 4][sqlserver -ag1] - PDK connector node released: HazelcastSourcePdkDataNode_636436a7-938f-401c-b112-b9aede3e3e79_1749700637362 
[TRACE] 2025-06-12 11:58:01.125 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] schema data cleaned 
[TRACE] 2025-06-12 11:58:01.128 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] monitor closed 
[TRACE] 2025-06-12 11:58:01.128 - [任务 4][sqlserver -ag1] - Node sqlserver -ag1[636436a7-938f-401c-b112-b9aede3e3e79] close complete, cost 3029 ms 
[TRACE] 2025-06-12 11:58:01.129 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] running status set to false 
[TRACE] 2025-06-12 11:58:01.133 - [任务 4][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_f5b25957-d42c-4aab-a2c0-5548acaf3f56_1749700637365 
[TRACE] 2025-06-12 11:58:01.133 - [任务 4][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_f5b25957-d42c-4aab-a2c0-5548acaf3f56_1749700637365 
[TRACE] 2025-06-12 11:58:01.133 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] schema data cleaned 
[TRACE] 2025-06-12 11:58:01.133 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] monitor closed 
[TRACE] 2025-06-12 11:58:01.337 - [任务 4][local_pg] - Node local_pg[f5b25957-d42c-4aab-a2c0-5548acaf3f56] close complete, cost 5 ms 
[TRACE] 2025-06-12 11:58:09.604 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-12 11:58:10.620 - [任务 4] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@2b1c3ec6 
[TRACE] 2025-06-12 11:58:10.621 - [任务 4] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b1c43d4 
[TRACE] 2025-06-12 11:58:10.743 - [任务 4] - Stop task milestones: 6837fcfe44fc7a7dfb48dbe4(任务 4)  
[TRACE] 2025-06-12 11:58:10.744 - [任务 4] - Stopped task aspect(s) 
[TRACE] 2025-06-12 11:58:10.745 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2025-06-12 11:58:10.745 - [任务 4] - Task stopped. 
[TRACE] 2025-06-12 11:58:10.786 - [任务 4] - Remove memory task client succeed, task: 任务 4[6837fcfe44fc7a7dfb48dbe4] 
[TRACE] 2025-06-12 11:58:10.788 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[6837fcfe44fc7a7dfb48dbe4] 
