[TRACE] 2025-04-27 16:53:34.183 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 16:53:34.249 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 16:53:34.249 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 16:53:34.309 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 16:53:34.309 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 16:53:34.359 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 16:53:34.359 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 16:53:34.359 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 16:53:34.359 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 16:53:34.360 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 16:53:34.360 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 16:53:34.840 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 16:53:34.841 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 16:53:34.841 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-04-27 16:53:34.841 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sync progress not exists, will run task as first time 
[INFO ] 2025-04-27 16:53:34.841 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 16:53:34.842 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sync progress not exists, will run task as first time 
[INFO ] 2025-04-27 16:53:34.946 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 16:53:34.948 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 16:53:34.948 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 16:53:34.948 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Sync progress not exists, will run task as first time 
[TRACE] 2025-04-27 16:53:34.948 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 16:53:35.140 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 16:53:35.140 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_74b0e993_da98_40d8_a922_ceb1f9339d9c 
[INFO ] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 16:53:35.196 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 16:53:35.217 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 16:53:35.218 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_74b0e993_da98_40d8_a922_ceb1f9339d9c 
[TRACE] 2025-04-27 16:53:35.624 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 16:56:13.094 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 16:56:13.369 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744014677 
[TRACE] 2025-04-27 16:56:13.370 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744014677 
[TRACE] 2025-04-27 16:56:13.370 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 16:56:13.370 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 16:56:13.370 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 16:56:13.370 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 370 ms 
[TRACE] 2025-04-27 16:56:13.371 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 16:56:13.398 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744014652 
[TRACE] 2025-04-27 16:56:13.398 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744014652 
[TRACE] 2025-04-27 16:56:13.399 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 16:56:13.400 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 16:56:13.400 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 28 ms 
[TRACE] 2025-04-27 16:56:15.493 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 16:56:15.493 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@272c9d0a 
[TRACE] 2025-04-27 16:56:15.612 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 16:56:15.612 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 16:56:15.612 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 16:56:15.612 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 16:56:20.619 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 16:56:20.619 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@272c9d0a 
[TRACE] 2025-04-27 16:56:20.622 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 16:56:20.622 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 16:56:20.660 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 16:56:20.662 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:00:24.003 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:00:24.059 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:00:24.060 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:00:24.141 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:00:24.142 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:00:24.156 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:00:24.156 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:00:24.157 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:00:24.157 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:00:24.157 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:00:24.157 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:00:24.584 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:00:24.584 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:00:24.584 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:00:24.585 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:00:24.651 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[INFO ] 2025-04-27 17:00:24.651 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:00:24.651 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:00:24.651 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:00:24.651 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 17:00:24.827 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:00:24.827 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_a4b27d58_82ba_43b4_9652_cb4eacefbda3 
[INFO ] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:00:24.894 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:00:24.911 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:00:24.911 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_a4b27d58_82ba_43b4_9652_cb4eacefbda3 
[TRACE] 2025-04-27 17:00:25.103 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:03:20.911 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:03:21.261 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:03:21.261 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744424419 
[TRACE] 2025-04-27 17:03:21.261 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744424419 
[TRACE] 2025-04-27 17:03:21.261 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:03:21.262 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:03:21.262 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 402 ms 
[TRACE] 2025-04-27 17:03:21.262 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:03:21.283 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744424439 
[TRACE] 2025-04-27 17:03:21.284 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744424439 
[TRACE] 2025-04-27 17:03:21.284 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:03:21.284 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:03:21.284 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 21 ms 
[TRACE] 2025-04-27 17:03:25.989 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:03:25.990 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58a31b6 
[TRACE] 2025-04-27 17:03:26.108 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:03:26.108 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:03:26.108 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:03:26.313 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:03:31.115 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:03:31.115 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@58a31b6 
[TRACE] 2025-04-27 17:03:31.115 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:03:31.115 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:03:31.321 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:03:31.321 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:06:20.191 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:06:20.396 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:06:20.442 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:06:20.532 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:06:20.532 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:06:20.663 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:06:20.663 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:06:20.663 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:06:20.664 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:06:20.664 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 1 ms 
[INFO ] 2025-04-27 17:06:20.869 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:06:21.170 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:06:21.170 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:06:21.171 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:06:21.171 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-27 17:06:21.181 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:06:21.181 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:06:21.181 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:06:21.183 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:06:21.349 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[WARN ] 2025-04-27 17:06:21.349 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:06:21.378 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_a386de6d_04be_4031_b1be_7bcf940869a9 
[INFO ] 2025-04-27 17:06:21.378 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:06:21.430 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:06:21.430 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:06:21.431 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:06:21.433 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:06:21.435 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:06:21.436 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:06:21.636 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_a386de6d_04be_4031_b1be_7bcf940869a9 
[TRACE] 2025-04-27 17:06:21.723 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:10:32.211 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:10:32.212 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:10:32.212 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744780962 
[TRACE] 2025-04-27 17:10:32.213 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745744780962 
[TRACE] 2025-04-27 17:10:32.214 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:10:32.216 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:10:32.217 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 35 ms 
[TRACE] 2025-04-27 17:10:32.217 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:10:32.226 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744780977 
[TRACE] 2025-04-27 17:10:32.227 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745744780977 
[TRACE] 2025-04-27 17:10:32.227 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:10:32.227 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:10:32.431 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 10 ms 
[TRACE] 2025-04-27 17:10:33.345 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:10:33.347 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3aea16d5 
[TRACE] 2025-04-27 17:10:33.348 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:10:33.462 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:10:33.462 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:10:33.668 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:10:38.476 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:10:38.477 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3aea16d5 
[TRACE] 2025-04-27 17:10:38.477 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:10:38.527 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:10:38.528 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:10:38.528 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:16:17.801 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:16:17.866 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:16:17.866 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:16:17.940 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:16:17.940 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:16:17.976 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:16:17.976 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:16:17.976 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:16:17.976 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:16:17.976 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:16:18.182 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:16:18.353 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:16:18.353 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:16:18.354 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:16:18.354 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:16:18.431 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[INFO ] 2025-04-27 17:16:18.431 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:16:18.431 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:16:18.432 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:16:18.432 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 17:16:18.591 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:16:18.592 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_5ebe0633_044d_44b2_886c_6f55cc49db67 
[INFO ] 2025-04-27 17:16:18.661 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:16:18.661 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:16:18.661 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:16:18.661 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:16:18.662 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:16:18.664 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:16:18.664 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:16:18.869 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_5ebe0633_044d_44b2_886c_6f55cc49db67 
[TRACE] 2025-04-27 17:16:18.911 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:18:08.611 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:18:08.613 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe 
[ERROR] 2025-04-27 17:18:08.623 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - java.net.SocketException: Broken pipe <-- Error Message -->
java.net.SocketException: Broken pipe

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe
	java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.SocketException: Broken pipe
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	at org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	at org.postgresql.util.internal.PgBufferedOutputStream.flush(PgBufferedOutputStream.java:48)
	at org.postgresql.core.PGStream.flush(PGStream.java:707)
	at org.postgresql.core.v3.QueryExecutorImpl.flushCopy(QueryExecutorImpl.java:1186)
	at org.postgresql.core.v3.CopyDualImpl.flushCopy(CopyDualImpl.java:33)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.updateStatusInternal(V3PGReplicationStream.java:198)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.forceUpdateStatus(V3PGReplicationStream.java:116)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.doFlushLsn(PostgresReplicationConnection.java:505)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.flushLsn(PostgresReplicationConnection.java:498)
	at io.debezium.connector.postgresql.PostgresStreamingChangeEventSource.commitOffset(PostgresStreamingChangeEventSource.java:330)
	at io.debezium.pipeline.ChangeEventSourceCoordinator.commitOffset(ChangeEventSourceCoordinator.java:158)
	at io.debezium.connector.common.BaseSourceTask.commit(BaseSourceTask.java:281)
	at io.debezium.embedded.EmbeddedEngine.commitOffsets(EmbeddedEngine.java:1003)
	at io.debezium.embedded.EmbeddedEngine.maybeFlush(EmbeddedEngine.java:977)
	at io.debezium.embedded.EmbeddedEngine$4.markBatchFinished(EmbeddedEngine.java:916)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.consumeRecords(PostgresCdcRunner.java:283)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:526)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-04-27 17:18:08.623 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Job suspend in error handle 
[TRACE] 2025-04-27 17:18:10.592 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-04-27 17:18:10.593 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:18:11.565 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745745378236 
[TRACE] 2025-04-27 17:18:11.567 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745745378236 
[TRACE] 2025-04-27 17:18:11.567 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:18:13.202 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:18:13.207 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 977 ms 
[TRACE] 2025-04-27 17:18:13.207 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[INFO ] 2025-04-27 17:18:26.360 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:18:27.996 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745745378219 
[TRACE] 2025-04-27 17:18:27.997 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745745378219 
[TRACE] 2025-04-27 17:18:27.998 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:18:27.999 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:18:27.999 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 14794 ms 
[TRACE] 2025-04-27 17:18:28.440 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup 
[WARN ] 2025-04-27 17:18:28.442 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Save error event failed: Failed to call rest api, msg no exception. 
[ERROR] 2025-04-27 17:18:28.442 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:771)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:862)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:828)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:809)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$23(HazelcastTargetPdkBaseNode.java:762)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:645)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:731)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:783)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:730)
	... 6 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1035)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:715)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:715)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:932)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:850)
	... 13 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: generalcolumnitemsetup
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$73(HazelcastTargetPdkDataNode.java:1123)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$74(HazelcastTargetPdkDataNode.java:1060)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 20 more
Caused by: java.lang.RuntimeException: Error occurred when retrying write record: io.tapdata.entity.event.dml.TapUpdateRecordEvent@4344cf2: {"after":{"columnid":12,"columndescription":"病?附件","columntype":"FILE","staticdatatype":"BINARY","columnsetting":"{\"required\": false, \"maxSize\": 10485760, \"acceptTypes\": \".pdf,.jpg,.png\", \"multiple\": true}","createby":"ADMIN002","createdtm":"1970-01-20T09:34:28.500","tsmp":"2023-02-10T14:35:00"},"before":{"columnid":12},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1745745407868,"tableId":"generalcolumnitemsetup","time":1745745408406,"type":302}
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:164)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:90)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1109)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$73(HazelcastTargetPdkDataNode.java:1105)
	... 27 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 该连接已关闭。
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.checkClosed(SQLServerConnection.java:1369)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.prepareStatement(SQLServerConnection.java:3989)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.prepareStatement(SQLServerConnection.java:3622)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at io.tapdata.common.dml.NormalWriteRecorder.justUpdate(NormalWriteRecorder.java:358)
	at io.tapdata.common.dml.NormalWriteRecorder.addUpdateBatch(NormalWriteRecorder.java:339)
	at io.tapdata.common.dml.NormalRecordWriter.writePart(NormalRecordWriter.java:140)
	... 32 more

[INFO ] 2025-04-27 17:18:29.927 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:18:29.927 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:18:29.928 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@32e020f 
[TRACE] 2025-04-27 17:18:29.928 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:18:30.058 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:18:30.058 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:18:30.058 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[INFO ] 2025-04-27 17:18:35.066 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:18:35.068 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:18:35.068 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@32e020f 
[TRACE] 2025-04-27 17:18:35.069 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:18:35.069 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:18:35.090 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:18:35.090 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:22:00.098 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:22:00.099 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:22:00.504 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:22:00.587 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:22:00.587 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:22:00.735 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:22:00.735 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:22:00.736 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:22:00.736 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:22:00.736 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:22:00.940 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:22:01.425 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:22:01.427 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:22:01.427 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:22:01.427 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-04-27 17:22:01.444 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:22:01.445 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:22:01.445 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:22:01.447 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:22:01.555 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[WARN ] 2025-04-27 17:22:01.555 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:22:01.577 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_393dc4ab_3b78_4580_b3ec_f230ab5a7c8c 
[INFO ] 2025-04-27 17:22:01.578 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:22:01.631 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:22:01.631 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:22:01.631 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:22:01.633 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:22:01.633 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:22:01.645 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:22:01.645 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_393dc4ab_3b78_4580_b3ec_f230ab5a7c8c 
[TRACE] 2025-04-27 17:22:02.052 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:27:04.205 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:27:04.221 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe 
[ERROR] 2025-04-27 17:27:04.222 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - java.net.SocketException: Broken pipe <-- Error Message -->
java.net.SocketException: Broken pipe

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe
	java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.SocketException: Broken pipe
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	at org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	at org.postgresql.util.internal.PgBufferedOutputStream.flush(PgBufferedOutputStream.java:48)
	at org.postgresql.core.PGStream.flush(PGStream.java:707)
	at org.postgresql.core.v3.QueryExecutorImpl.flushCopy(QueryExecutorImpl.java:1186)
	at org.postgresql.core.v3.CopyDualImpl.flushCopy(CopyDualImpl.java:33)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.updateStatusInternal(V3PGReplicationStream.java:198)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.forceUpdateStatus(V3PGReplicationStream.java:116)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.doFlushLsn(PostgresReplicationConnection.java:505)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.flushLsn(PostgresReplicationConnection.java:498)
	at io.debezium.connector.postgresql.PostgresStreamingChangeEventSource.commitOffset(PostgresStreamingChangeEventSource.java:330)
	at io.debezium.pipeline.ChangeEventSourceCoordinator.commitOffset(ChangeEventSourceCoordinator.java:158)
	at io.debezium.connector.common.BaseSourceTask.commit(BaseSourceTask.java:281)
	at io.debezium.embedded.EmbeddedEngine.commitOffsets(EmbeddedEngine.java:1003)
	at io.debezium.embedded.EmbeddedEngine.maybeFlush(EmbeddedEngine.java:977)
	at io.debezium.embedded.EmbeddedEngine$4.markBatchFinished(EmbeddedEngine.java:916)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.consumeRecords(PostgresCdcRunner.java:271)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:526)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-04-27 17:27:04.222 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Job suspend in error handle 
[TRACE] 2025-04-27 17:27:04.284 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-04-27 17:27:04.294 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:27:04.294 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745745720987 
[TRACE] 2025-04-27 17:27:04.295 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745745720987 
[TRACE] 2025-04-27 17:27:04.300 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:27:04.300 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:27:04.300 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 10 ms 
[TRACE] 2025-04-27 17:27:04.300 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:27:04.313 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745745721014 
[TRACE] 2025-04-27 17:27:04.313 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745745721014 
[TRACE] 2025-04-27 17:27:04.313 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:27:04.314 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:27:04.519 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 16 ms 
[INFO ] 2025-04-27 17:27:09.230 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:27:09.232 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:27:09.236 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6f8872b6 
[TRACE] 2025-04-27 17:27:09.237 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:27:09.366 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:27:09.367 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:27:09.572 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[INFO ] 2025-04-27 17:27:14.380 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:27:14.382 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:27:14.382 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6f8872b6 
[TRACE] 2025-04-27 17:27:14.382 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:27:14.383 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:27:14.416 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:27:14.419 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:28:05.353 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:28:05.354 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:28:05.494 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:28:05.494 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:28:05.548 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:28:05.548 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:28:05.580 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:28:05.580 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:28:05.587 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:28:05.587 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:28:05.792 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:28:06.039 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:28:06.039 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:28:06.039 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:28:06.103 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:28:06.103 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[INFO ] 2025-04-27 17:28:06.145 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:28:06.146 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:28:06.146 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:28:06.268 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 17:28:06.268 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:28:06.293 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_fc1933be_e533_4271_8f2a_25f15022dc13 
[INFO ] 2025-04-27 17:28:06.293 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:28:06.348 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:28:06.349 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:28:06.349 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:28:06.349 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:28:06.349 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:28:06.362 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:28:06.363 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_fc1933be_e533_4271_8f2a_25f15022dc13 
[TRACE] 2025-04-27 17:28:06.564 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:38:12.250 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:38:12.251 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.net.SocketException: Broken pipe 
[ERROR] 2025-04-27 17:38:12.762 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - java.net.SocketException: Broken pipe <-- Error Message -->
java.net.SocketException: Broken pipe

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Broken pipe
	java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	...

<-- Full Stack Trace -->
java.net.SocketException: Broken pipe
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.net.SocketException: Broken pipe
	at java.base/sun.nio.ch.NioSocketImpl.implWrite(NioSocketImpl.java:425)
	at java.base/sun.nio.ch.NioSocketImpl.write(NioSocketImpl.java:445)
	at java.base/sun.nio.ch.NioSocketImpl$2.write(NioSocketImpl.java:831)
	at java.base/java.net.Socket$SocketOutputStream.write(Socket.java:1045)
	at org.postgresql.util.internal.PgBufferedOutputStream.flushBuffer(PgBufferedOutputStream.java:41)
	at org.postgresql.util.internal.PgBufferedOutputStream.flush(PgBufferedOutputStream.java:48)
	at org.postgresql.core.PGStream.flush(PGStream.java:707)
	at org.postgresql.core.v3.QueryExecutorImpl.flushCopy(QueryExecutorImpl.java:1186)
	at org.postgresql.core.v3.CopyDualImpl.flushCopy(CopyDualImpl.java:33)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.updateStatusInternal(V3PGReplicationStream.java:198)
	at org.postgresql.core.v3.replication.V3PGReplicationStream.forceUpdateStatus(V3PGReplicationStream.java:116)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.doFlushLsn(PostgresReplicationConnection.java:505)
	at io.debezium.connector.postgresql.connection.PostgresReplicationConnection$1.flushLsn(PostgresReplicationConnection.java:498)
	at io.debezium.connector.postgresql.PostgresStreamingChangeEventSource.commitOffset(PostgresStreamingChangeEventSource.java:330)
	at io.debezium.pipeline.ChangeEventSourceCoordinator.commitOffset(ChangeEventSourceCoordinator.java:158)
	at io.debezium.connector.common.BaseSourceTask.commit(BaseSourceTask.java:281)
	at io.debezium.embedded.EmbeddedEngine.commitOffsets(EmbeddedEngine.java:1003)
	at io.debezium.embedded.EmbeddedEngine.maybeFlush(EmbeddedEngine.java:977)
	at io.debezium.embedded.EmbeddedEngine$4.markBatchFinished(EmbeddedEngine.java:916)
	at io.tapdata.connector.postgres.cdc.PostgresCdcRunner.consumeRecords(PostgresCdcRunner.java:271)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.postgres.cdc.DebeziumCdcRunner.startCdcRunner(DebeziumCdcRunner.java:44)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:526)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-04-27 17:38:12.762 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Job suspend in error handle 
[TRACE] 2025-04-27 17:38:12.857 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Exception skipping - The current exception does not match the skip exception strategy, message: Cannot invoke "java.util.concurrent.atomic.AtomicInteger.intValue()" because "sourceInitialCounter" is null 
[TRACE] 2025-04-27 17:38:12.863 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:38:12.864 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745746085918 
[TRACE] 2025-04-27 17:38:12.864 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745746085918 
[TRACE] 2025-04-27 17:38:12.864 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:38:12.865 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:38:12.865 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 6 ms 
[TRACE] 2025-04-27 17:38:12.865 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:38:12.877 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745746085904 
[TRACE] 2025-04-27 17:38:12.877 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745746085904 
[TRACE] 2025-04-27 17:38:12.878 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:38:12.882 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:38:12.883 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 17 ms 
[INFO ] 2025-04-27 17:38:16.430 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:38:16.430 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:38:16.431 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c87726a 
[TRACE] 2025-04-27 17:38:16.432 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:38:16.555 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:38:16.555 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:38:16.555 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[INFO ] 2025-04-27 17:38:21.568 - [GeneralColumnItemSetup - Copy] - Task [GeneralColumnItemSetup - Copy] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-04-27 17:38:21.569 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:38:21.570 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c87726a 
[TRACE] 2025-04-27 17:38:21.571 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:38:21.571 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:38:21.599 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:38:21.599 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:51:42.802 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:51:42.803 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:51:43.005 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:51:43.130 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:51:43.130 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:51:43.168 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:51:43.168 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:51:43.168 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:51:43.168 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:51:43.169 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:51:43.169 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:51:43.621 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:51:43.621 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:51:43.621 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:51:43.621 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:51:43.657 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[INFO ] 2025-04-27 17:51:43.657 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:51:43.657 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:51:43.657 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:51:43.786 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 17:51:43.786 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:51:43.807 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_df5ffc0b_2de0_484d_ad1a_e5f898c32b82 
[INFO ] 2025-04-27 17:51:43.807 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:51:43.859 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:51:43.859 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:51:43.859 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:51:43.859 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:51:43.860 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:51:43.860 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:51:44.019 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_df5ffc0b_2de0_484d_ad1a_e5f898c32b82 
[TRACE] 2025-04-27 17:51:44.019 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:53:32.528 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:53:32.834 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:53:32.834 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745747503462 
[TRACE] 2025-04-27 17:53:32.834 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745747503462 
[TRACE] 2025-04-27 17:53:32.834 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:53:32.835 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:53:32.836 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 379 ms 
[TRACE] 2025-04-27 17:53:32.836 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:53:32.855 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745747503445 
[TRACE] 2025-04-27 17:53:32.855 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745747503445 
[TRACE] 2025-04-27 17:53:32.855 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:53:32.855 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:53:33.059 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 20 ms 
[TRACE] 2025-04-27 17:53:34.510 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:53:34.511 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74b57efe 
[TRACE] 2025-04-27 17:53:34.631 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:53:34.631 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:53:34.631 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:53:34.631 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:53:39.649 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:53:39.650 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@74b57efe 
[TRACE] 2025-04-27 17:53:39.650 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:53:39.650 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:53:39.712 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:53:39.715 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:54:21.254 - [GeneralColumnItemSetup - Copy] - Task initialization... 
[TRACE] 2025-04-27 17:54:21.321 - [GeneralColumnItemSetup - Copy] - Start task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy) 
[INFO ] 2025-04-27 17:54:21.321 - [GeneralColumnItemSetup - Copy] - Loading table structure completed 
[TRACE] 2025-04-27 17:54:21.383 - [GeneralColumnItemSetup - Copy] - Node performs snapshot read asynchronously 
[TRACE] 2025-04-27 17:54:21.383 - [GeneralColumnItemSetup - Copy] - The engine receives GeneralColumnItemSetup - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2025-04-27 17:54:21.403 - [GeneralColumnItemSetup - Copy] - Task started 
[TRACE] 2025-04-27 17:54:21.403 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:54:21.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] start preload schema,table counts: 1 
[TRACE] 2025-04-27 17:54:21.403 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] preload schema finished, cost 0 ms 
[TRACE] 2025-04-27 17:54:21.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] preload schema finished, cost 0 ms 
[INFO ] 2025-04-27 17:54:21.404 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Enable partition table support for source database 
[INFO ] 2025-04-27 17:54:21.792 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Sink connector(sqlserver_cmis) initialization completed 
[TRACE] 2025-04-27 17:54:21.792 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node(sqlserver_cmis) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-04-27 17:54:21.792 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-04-27 17:54:21.793 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Apply table structure to target database 
[TRACE] 2025-04-27 17:54:21.881 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - The table generalcolumnitemsetup has already exist. 
[INFO ] 2025-04-27 17:54:21.881 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source connector(pg_jerryTest) initialization completed 
[TRACE] 2025-04-27 17:54:21.881 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" read batch size: 100 
[TRACE] 2025-04-27 17:54:21.881 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Source node "pg_jerryTest" event queue capacity: 200 
[TRACE] 2025-04-27 17:54:21.882 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-04-27 17:54:22.255 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:54:22.511 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - new logical replication slot created, slotName:tapdata_cdc_4f2ab0b6_293f_47e4_95c9_36d17e9ef8ec 
[INFO ] 2025-04-27 17:54:22.586 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:54:22.586 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Batch read completed. 
[TRACE] 2025-04-27 17:54:22.586 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync starting... 
[TRACE] 2025-04-27 17:54:22.586 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Initial sync completed 
[TRACE] 2025-04-27 17:54:22.587 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting stream read, table list: [generalcolumnitemsetup], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-04-27 17:54:22.587 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Starting incremental sync using database log parser 
[WARN ] 2025-04-27 17:54:22.788 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-04-27 17:54:22.990 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Using an existing logical replication slot, slotName:tapdata_cdc_4f2ab0b6_293f_47e4_95c9_36d17e9ef8ec 
[TRACE] 2025-04-27 17:54:24.622 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Connector PostgreSQL incremental start succeed, tables: [generalcolumnitemsetup], data change syncing 
[TRACE] 2025-04-27 17:55:22.364 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] running status set to false 
[TRACE] 2025-04-27 17:55:22.377 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Incremental sync completed 
[TRACE] 2025-04-27 17:55:22.377 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node stopped: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745747661649 
[TRACE] 2025-04-27 17:55:22.377 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - PDK connector node released: HazelcastSourcePdkDataNode_ff0515c9-eff3-41e2-b2ca-db224b849eab_1745747661649 
[TRACE] 2025-04-27 17:55:22.377 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] schema data cleaned 
[TRACE] 2025-04-27 17:55:22.378 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] monitor closed 
[TRACE] 2025-04-27 17:55:22.378 - [GeneralColumnItemSetup - Copy][pg_jerryTest] - Node pg_jerryTest[ff0515c9-eff3-41e2-b2ca-db224b849eab] close complete, cost 16 ms 
[TRACE] 2025-04-27 17:55:22.378 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] running status set to false 
[TRACE] 2025-04-27 17:55:22.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745747661662 
[TRACE] 2025-04-27 17:55:22.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - PDK connector node released: HazelcastTargetPdkDataNode_4d328d37-2261-4f21-a266-82caf517eca8_1745747661662 
[TRACE] 2025-04-27 17:55:22.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] schema data cleaned 
[TRACE] 2025-04-27 17:55:22.403 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] monitor closed 
[TRACE] 2025-04-27 17:55:22.404 - [GeneralColumnItemSetup - Copy][sqlserver_cmis] - Node sqlserver_cmis[4d328d37-2261-4f21-a266-82caf517eca8] close complete, cost 25 ms 
[TRACE] 2025-04-27 17:55:23.718 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:55:23.720 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1903bef2 
[TRACE] 2025-04-27 17:55:23.720 - [GeneralColumnItemSetup - Copy] - Stop task milestones: 680df0616d43f73568ddfa0d(GeneralColumnItemSetup - Copy)  
[TRACE] 2025-04-27 17:55:23.836 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[TRACE] 2025-04-27 17:55:23.836 - [GeneralColumnItemSetup - Copy] - Snapshot order controller have been removed 
[INFO ] 2025-04-27 17:55:23.836 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:55:28.850 - [GeneralColumnItemSetup - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-04-27 17:55:28.850 - [GeneralColumnItemSetup - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1903bef2 
[TRACE] 2025-04-27 17:55:28.850 - [GeneralColumnItemSetup - Copy] - Stopped task aspect(s) 
[INFO ] 2025-04-27 17:55:28.851 - [GeneralColumnItemSetup - Copy] - Task stopped. 
[TRACE] 2025-04-27 17:55:28.897 - [GeneralColumnItemSetup - Copy] - Remove memory task client succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
[TRACE] 2025-04-27 17:55:28.900 - [GeneralColumnItemSetup - Copy] - Destroy memory task client cache succeed, task: GeneralColumnItemSetup - Copy[680df0616d43f73568ddfa0d] 
