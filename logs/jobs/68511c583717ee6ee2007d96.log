[TRACE] 2025-06-20 09:09:14.785 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:14.787 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:14.787 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:14.787 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:14.787 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:14.787 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:14.788 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:18.397 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:18.397 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:18.398 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:18.398 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:18.398 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:18.398 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:18.398 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:21.743 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:21.744 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:22.502 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:22.503 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:22.503 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:22.503 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:22.503 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:22.503 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:22.708 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:26.202 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:26.202 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:26.202 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:26.203 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.203 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.203 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.203 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:26.759 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:27.993 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:27.993 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:09:27.993 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:27.993 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:09:27.994 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:09:27.994 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:09:27.994 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.347 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.460 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:34.460 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:34.465 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381834358 
[TRACE] 2025-06-20 09:10:34.465 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381834358 
[TRACE] 2025-06-20 09:10:34.465 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:34.465 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:34.669 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:34.846 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:34.846 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-d1578908-de8d-4b4a-b704-505734981c31 
[INFO ] 2025-06-20 09:10:34.846 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-d1578908-de8d-4b4a-b704-505734981c31 
[INFO ] 2025-06-20 09:10:34.846 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:34.849 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:34.849 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:34.849 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:34.866 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] running status set to false 
[TRACE] 2025-06-20 09:10:34.866 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] schema data cleaned 
[TRACE] 2025-06-20 09:10:34.866 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] monitor closed 
[TRACE] 2025-06-20 09:10:34.868 - [任务 16(100)][1031dead-08db-418e-962b-853022a8ca64] - Node 1031dead-08db-418e-962b-853022a8ca64[1031dead-08db-418e-962b-853022a8ca64] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.875 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:34.875 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:34.875 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:34.875 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:34.925 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:35.128 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:35.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:35.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381835111 
[TRACE] 2025-06-20 09:10:35.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381835111 
[TRACE] 2025-06-20 09:10:35.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:35.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:10:35.399 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:35.405 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-aad14652-6279-4b03-9bbb-0b1c0c2e6e89 
[INFO ] 2025-06-20 09:10:35.405 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-aad14652-6279-4b03-9bbb-0b1c0c2e6e89 
[INFO ] 2025-06-20 09:10:35.405 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.406 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.406 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:35.406 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:35.414 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] running status set to false 
[TRACE] 2025-06-20 09:10:35.415 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.415 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] monitor closed 
[TRACE] 2025-06-20 09:10:35.415 - [任务 16(100)][7e5268f4-5d58-4fe6-a43e-6d01270dc543] - Node 7e5268f4-5d58-4fe6-a43e-6d01270dc543[7e5268f4-5d58-4fe6-a43e-6d01270dc543] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:35.416 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:35.416 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:35.416 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:35.417 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:35.514 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:35.514 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:35.514 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:35.515 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:35.515 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:35.515 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:35.515 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:35.778 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:35.778 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381835661 
[TRACE] 2025-06-20 09:10:35.779 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381835661 
[TRACE] 2025-06-20 09:10:35.779 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.779 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:35.779 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:35.947 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:35.947 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-5b54e061-f171-4736-b39a-b45fc7b50867 
[INFO ] 2025-06-20 09:10:35.947 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-5b54e061-f171-4736-b39a-b45fc7b50867 
[INFO ] 2025-06-20 09:10:35.947 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.948 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.949 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:35.949 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:35.956 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] running status set to false 
[TRACE] 2025-06-20 09:10:35.956 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] schema data cleaned 
[TRACE] 2025-06-20 09:10:35.957 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] monitor closed 
[TRACE] 2025-06-20 09:10:35.957 - [任务 16(100)][b4cfd0f7-7909-4287-a035-7178f307f376] - Node b4cfd0f7-7909-4287-a035-7178f307f376[b4cfd0f7-7909-4287-a035-7178f307f376] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:35.958 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:35.958 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:35.958 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:36.162 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:40.080 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.081 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.081 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:40.081 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.082 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.082 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.182 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:40.182 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:40.184 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381840103 
[TRACE] 2025-06-20 09:10:40.184 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381840103 
[TRACE] 2025-06-20 09:10:40.184 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.184 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:40.185 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:40.320 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:40.320 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-cbd3cb56-0a87-4804-b5c9-fdee592489ba 
[INFO ] 2025-06-20 09:10:40.320 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-cbd3cb56-0a87-4804-b5c9-fdee592489ba 
[INFO ] 2025-06-20 09:10:40.320 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.321 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.321 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:40.321 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:40.326 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] running status set to false 
[TRACE] 2025-06-20 09:10:40.326 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.327 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] monitor closed 
[TRACE] 2025-06-20 09:10:40.327 - [任务 16(100)][0618928d-b408-49d6-b5f1-ae820b934189] - Node 0618928d-b408-49d6-b5f1-ae820b934189[0618928d-b408-49d6-b5f1-ae820b934189] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.327 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:40.327 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:40.328 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:40.328 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:40.375 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:40.375 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.376 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.376 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.376 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.376 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.376 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:40.488 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:40.488 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381840393 
[TRACE] 2025-06-20 09:10:40.488 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381840393 
[TRACE] 2025-06-20 09:10:40.488 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.488 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:40.627 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:40.627 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:40.632 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1926eb61-18ea-4779-949d-b698bdb92736 
[INFO ] 2025-06-20 09:10:40.632 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1926eb61-18ea-4779-949d-b698bdb92736 
[INFO ] 2025-06-20 09:10:40.632 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.633 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.633 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:40.633 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:40.633 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] running status set to false 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] monitor closed 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16(100)][d6c17535-3bd7-44ce-b857-97d27897c9ca] - Node d6c17535-3bd7-44ce-b857-97d27897c9ca[d6c17535-3bd7-44ce-b857-97d27897c9ca] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:40.635 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:40.635 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:40.700 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.700 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:40.700 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:40.701 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.701 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.701 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.811 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:40.811 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:40.816 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381840720 
[TRACE] 2025-06-20 09:10:40.816 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381840720 
[TRACE] 2025-06-20 09:10:40.816 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.817 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:40.977 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:40.977 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:40.983 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e0a7c2f3-5634-4619-8037-3886509e8be9 
[INFO ] 2025-06-20 09:10:40.983 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e0a7c2f3-5634-4619-8037-3886509e8be9 
[INFO ] 2025-06-20 09:10:40.983 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.984 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.984 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:40.984 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] running status set to false 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] schema data cleaned 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] monitor closed 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16(100)][03679687-8422-403a-9f07-934ff32ca99d] - Node 03679687-8422-403a-9f07-934ff32ca99d[03679687-8422-403a-9f07-934ff32ca99d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:40.988 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:40.988 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:41.192 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:41.480 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:41.480 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:41.480 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:41.481 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.481 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.481 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.481 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:41.592 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:41.592 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381841496 
[TRACE] 2025-06-20 09:10:41.592 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381841496 
[TRACE] 2025-06-20 09:10:41.592 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:41.593 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:41.593 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:41.727 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:41.729 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-0c7d98ff-ae68-4a6c-93a3-6749dda18cd8 
[INFO ] 2025-06-20 09:10:41.729 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-0c7d98ff-ae68-4a6c-93a3-6749dda18cd8 
[INFO ] 2025-06-20 09:10:41.729 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:41.730 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:41.730 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:41.730 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:41.735 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] running status set to false 
[TRACE] 2025-06-20 09:10:41.735 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] schema data cleaned 
[TRACE] 2025-06-20 09:10:41.735 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] monitor closed 
[TRACE] 2025-06-20 09:10:41.736 - [任务 16(100)][9f117654-d4bd-4263-9781-b2c47be4a6d3] - Node 9f117654-d4bd-4263-9781-b2c47be4a6d3[9f117654-d4bd-4263-9781-b2c47be4a6d3] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.736 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:41.736 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:41.736 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:41.785 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:41.785 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:41.785 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:41.785 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:41.785 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.786 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.786 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:41.786 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:41.891 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:41.896 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381841802 
[TRACE] 2025-06-20 09:10:41.896 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381841802 
[TRACE] 2025-06-20 09:10:41.896 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:41.896 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:42.035 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:42.035 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:42.041 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a1c181b0-6e17-43b9-97b3-08dafed6f54b 
[INFO ] 2025-06-20 09:10:42.041 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a1c181b0-6e17-43b9-97b3-08dafed6f54b 
[INFO ] 2025-06-20 09:10:42.042 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.042 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.042 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:42.042 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:42.043 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] running status set to false 
[TRACE] 2025-06-20 09:10:42.043 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.043 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] monitor closed 
[TRACE] 2025-06-20 09:10:42.043 - [任务 16(100)][fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] - Node fb9e3afe-34b4-412f-b13e-0747f1c9fbaf[fb9e3afe-34b4-412f-b13e-0747f1c9fbaf] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:42.044 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:42.044 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:42.044 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:42.044 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:42.118 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:42.119 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:42.227 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:42.227 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381842134 
[TRACE] 2025-06-20 09:10:42.227 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381842134 
[TRACE] 2025-06-20 09:10:42.227 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.227 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:42.366 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:42.366 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:42.369 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1c7cd286-2cc8-4102-9688-9662c8178f95 
[INFO ] 2025-06-20 09:10:42.370 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1c7cd286-2cc8-4102-9688-9662c8178f95 
[INFO ] 2025-06-20 09:10:42.370 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.370 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.370 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:42.370 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] running status set to false 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] schema data cleaned 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] monitor closed 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)][546148dd-60b2-433b-a416-2d44fa555807] - Node 546148dd-60b2-433b-a416-2d44fa555807[546148dd-60b2-433b-a416-2d44fa555807] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:42.379 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:42.379 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.447 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:44.553 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:44.557 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381844464 
[TRACE] 2025-06-20 09:10:44.557 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381844464 
[TRACE] 2025-06-20 09:10:44.557 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.557 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:44.558 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:44.624 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.624 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.625 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.625 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.625 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:44.625 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:44.625 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.702 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:44.704 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-061088ff-6eb3-42c1-8716-56b1b1661abb 
[INFO ] 2025-06-20 09:10:44.704 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-061088ff-6eb3-42c1-8716-56b1b1661abb 
[INFO ] 2025-06-20 09:10:44.705 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.705 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.705 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:44.714 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:44.714 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] running status set to false 
[TRACE] 2025-06-20 09:10:44.714 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.714 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] monitor closed 
[TRACE] 2025-06-20 09:10:44.714 - [任务 16(100)][ba28802f-8b9e-4dcc-be13-aab8372d3d2d] - Node ba28802f-8b9e-4dcc-be13-aab8372d3d2d[ba28802f-8b9e-4dcc-be13-aab8372d3d2d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.715 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:44.715 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:44.715 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.764 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:44.778 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:44.778 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381844642 
[TRACE] 2025-06-20 09:10:44.778 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381844642 
[TRACE] 2025-06-20 09:10:44.779 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.779 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:44.779 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:44.878 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:44.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381844790 
[TRACE] 2025-06-20 09:10:44.886 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381844790 
[TRACE] 2025-06-20 09:10:44.886 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.886 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:44.886 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-7f8f899f-c287-4eb4-89a7-6b983bfd4498 
[INFO ] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-7f8f899f-c287-4eb4-89a7-6b983bfd4498 
[INFO ] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.947 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:44.951 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:44.951 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] running status set to false 
[TRACE] 2025-06-20 09:10:44.951 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] schema data cleaned 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] monitor closed 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16(100)][97cf41bb-6a50-4045-a60c-e552c11317f3] - Node 97cf41bb-6a50-4045-a60c-e552c11317f3[97cf41bb-6a50-4045-a60c-e552c11317f3] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:44.996 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.029 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:45.029 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:45.032 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e5c9d4b5-bf61-4415-9b65-b746406cb94d 
[INFO ] 2025-06-20 09:10:45.032 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e5c9d4b5-bf61-4415-9b65-b746406cb94d 
[INFO ] 2025-06-20 09:10:45.032 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.032 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.032 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] running status set to false 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] monitor closed 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)][3d3ea2e9-a66e-435c-92ed-efa86dd7d111] - Node 3d3ea2e9-a66e-435c-92ed-efa86dd7d111[3d3ea2e9-a66e-435c-92ed-efa86dd7d111] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381845012 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381845012 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:45.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:45.121 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.121 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.122 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.122 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:45.122 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.122 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.122 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:45.141 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:45.141 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381754951 
[TRACE] 2025-06-20 09:10:45.141 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381754951 
[TRACE] 2025-06-20 09:10:45.142 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.142 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:45.222 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:45.222 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:45.224 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845139 
[TRACE] 2025-06-20 09:10:45.224 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845139 
[TRACE] 2025-06-20 09:10:45.224 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.224 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:45.224 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:45.292 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:45.293 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-68c79aba-ed8e-4a54-9fea-cb523cb23a81 
[INFO ] 2025-06-20 09:10:45.294 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-68c79aba-ed8e-4a54-9fea-cb523cb23a81 
[INFO ] 2025-06-20 09:10:45.294 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.294 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.295 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:45.301 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:45.301 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] running status set to false 
[TRACE] 2025-06-20 09:10:45.301 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.301 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] monitor closed 
[TRACE] 2025-06-20 09:10:45.302 - [任务 16(100)][61ed5719-8616-468e-9ca9-16f590969123] - Node 61ed5719-8616-468e-9ca9-16f590969123[61ed5719-8616-468e-9ca9-16f590969123] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.302 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.302 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.302 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.348 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:45.348 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:45.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.349 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.349 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.349 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.375 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:45.375 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[TRACE] 2025-06-20 09:10:45.382 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:45.382 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-423a304a-3a3e-45c1-99b9-391ec51b8a95 
[INFO ] 2025-06-20 09:10:45.382 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-423a304a-3a3e-45c1-99b9-391ec51b8a95 
[INFO ] 2025-06-20 09:10:45.382 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] running status set to false 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] monitor closed 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16(100)][829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] - Node 829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c[829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.384 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.384 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.384 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.384 - [任务 16(100)] - Stopped task aspect(s) 
[INFO ] 2025-06-20 09:10:45.386 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-4b807103-d55f-40b8-9e85-d6ab7831f817 
[INFO ] 2025-06-20 09:10:45.386 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-4b807103-d55f-40b8-9e85-d6ab7831f817 
[INFO ] 2025-06-20 09:10:45.387 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.387 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.387 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:45.387 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:45.389 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] running status set to false 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] monitor closed 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)][167ff8ee-6220-49f6-ba22-70a28c9f753a] - Node 167ff8ee-6220-49f6-ba22-70a28c9f753a[167ff8ee-6220-49f6-ba22-70a28c9f753a] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.445 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.484 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:45.484 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:45.491 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381845400 
[TRACE] 2025-06-20 09:10:45.491 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381845400 
[TRACE] 2025-06-20 09:10:45.491 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.491 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:45.558 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:45.558 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:45.559 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845461 
[TRACE] 2025-06-20 09:10:45.559 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845461 
[TRACE] 2025-06-20 09:10:45.559 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.559 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:45.625 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:45.625 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-10f746f6-ad92-4a51-9e7c-810b5338adc0 
[INFO ] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-10f746f6-ad92-4a51-9e7c-810b5338adc0 
[INFO ] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:45.628 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] running status set to false 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] monitor closed 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)][b6f02481-98e2-4a7a-a35b-327ee0fec1f4] - Node b6f02481-98e2-4a7a-a35b-327ee0fec1f4[b6f02481-98e2-4a7a-a35b-327ee0fec1f4] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.692 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.705 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:45.705 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:45.708 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b47a80c3-cb3f-463f-ad3c-e6e52af74c06 
[INFO ] 2025-06-20 09:10:45.708 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b47a80c3-cb3f-463f-ad3c-e6e52af74c06 
[INFO ] 2025-06-20 09:10:45.708 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.709 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.709 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:45.709 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:10:45.711 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] running status set to false 
[TRACE] 2025-06-20 09:10:45.711 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.711 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] monitor closed 
[TRACE] 2025-06-20 09:10:45.711 - [任务 16(100)][f016637c-da99-40a3-bc3d-e366f64a394b] - Node f016637c-da99-40a3-bc3d-e366f64a394b[f016637c-da99-40a3-bc3d-e366f64a394b] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:45.712 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:45.712 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:45.712 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:45.712 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:45.811 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:45.811 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845718 
[TRACE] 2025-06-20 09:10:45.812 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381845718 
[TRACE] 2025-06-20 09:10:45.812 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.812 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:45.812 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:45.996 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:45.998 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-672b05aa-6347-4513-933f-6f458da0e5a1 
[INFO ] 2025-06-20 09:10:45.998 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-672b05aa-6347-4513-933f-6f458da0e5a1 
[INFO ] 2025-06-20 09:10:45.998 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.999 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:45.999 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:45.999 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:46.012 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] running status set to false 
[TRACE] 2025-06-20 09:10:46.012 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] schema data cleaned 
[TRACE] 2025-06-20 09:10:46.012 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] monitor closed 
[TRACE] 2025-06-20 09:10:46.012 - [任务 16(100)][6ff4498b-4922-4bc6-b862-4cea04caec4c] - Node 6ff4498b-4922-4bc6-b862-4cea04caec4c[6ff4498b-4922-4bc6-b862-4cea04caec4c] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:46.013 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:46.013 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:46.013 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:46.013 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:47.814 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:47.815 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:47.921 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:47.923 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381847830 
[TRACE] 2025-06-20 09:10:47.923 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381847830 
[TRACE] 2025-06-20 09:10:47.923 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:47.923 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:47.923 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:47.998 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:47.998 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:47.998 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:47.999 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:47.999 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:47.999 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.052 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:48.052 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-74795101-ff41-4748-bde6-6a128cde25eb 
[INFO ] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-74795101-ff41-4748-bde6-6a128cde25eb 
[INFO ] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:48.053 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] running status set to false 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] monitor closed 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)][6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] - Node 6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff[6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.063 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.113 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.118 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:48.118 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:48.122 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381848017 
[TRACE] 2025-06-20 09:10:48.122 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381848017 
[TRACE] 2025-06-20 09:10:48.122 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.122 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:48.122 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848133 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848133 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:48.232 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:48.288 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:48.288 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-dee96120-e704-4455-a9aa-7ffe231f565c 
[INFO ] 2025-06-20 09:10:48.288 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-dee96120-e704-4455-a9aa-7ffe231f565c 
[INFO ] 2025-06-20 09:10:48.289 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.289 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.289 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:48.289 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:48.296 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] running status set to false 
[TRACE] 2025-06-20 09:10:48.296 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.296 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] monitor closed 
[TRACE] 2025-06-20 09:10:48.296 - [任务 16(100)][c3552f2c-350b-44ce-aeb5-6aeb7061a673] - Node c3552f2c-350b-44ce-aeb5-6aeb7061a673[c3552f2c-350b-44ce-aeb5-6aeb7061a673] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.297 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.297 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.297 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.345 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.378 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:48.378 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:48.380 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-ced21fbc-a685-4430-bfcc-4291f607819c 
[INFO ] 2025-06-20 09:10:48.380 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-ced21fbc-a685-4430-bfcc-4291f607819c 
[INFO ] 2025-06-20 09:10:48.380 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:48.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] running status set to false 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] monitor closed 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)][4f8d650b-63c4-497d-866d-7eefb48cdd95] - Node 4f8d650b-63c4-497d-866d-7eefb48cdd95[4f8d650b-63c4-497d-866d-7eefb48cdd95] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.386 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.386 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.448 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.453 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:48.453 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:48.455 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848371 
[TRACE] 2025-06-20 09:10:48.455 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848371 
[TRACE] 2025-06-20 09:10:48.455 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.455 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:48.455 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381758408 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381758408 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:48.502 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381848467 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381848467 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:48.548 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:48.644 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:48.644 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b408e822-e1fd-4274-9592-15062fd1480a 
[INFO ] 2025-06-20 09:10:48.644 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b408e822-e1fd-4274-9592-15062fd1480a 
[INFO ] 2025-06-20 09:10:48.645 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.645 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.645 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:48.645 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:48.651 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] running status set to false 
[TRACE] 2025-06-20 09:10:48.651 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.651 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] monitor closed 
[TRACE] 2025-06-20 09:10:48.651 - [任务 16(100)][5cb1c5a2-22b4-43e2-92e2-6b723312af88] - Node 5cb1c5a2-22b4-43e2-92e2-6b723312af88[5cb1c5a2-22b4-43e2-92e2-6b723312af88] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.653 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.653 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.653 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.653 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a356c931-a855-4938-a70d-93c9a3d7397d 
[INFO ] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a356c931-a855-4938-a70d-93c9a3d7397d 
[INFO ] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.662 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:48.663 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.671 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] running status set to false 
[TRACE] 2025-06-20 09:10:48.671 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] monitor closed 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16(100)][5542c0ad-2917-46db-b649-98bd41316e35] - Node 5542c0ad-2917-46db-b649-98bd41316e35[5542c0ad-2917-46db-b649-98bd41316e35] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.714 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:48.715 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.730 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:48.730 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-0c92a363-f629-495b-8483-654614daf30e 
[INFO ] 2025-06-20 09:10:48.730 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-0c92a363-f629-495b-8483-654614daf30e 
[INFO ] 2025-06-20 09:10:48.730 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.731 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.731 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:48.731 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.737 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] running status set to false 
[TRACE] 2025-06-20 09:10:48.737 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.737 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] monitor closed 
[TRACE] 2025-06-20 09:10:48.737 - [任务 16(100)][750112f0-7bf7-4eda-8a18-235fbdff37a6] - Node 750112f0-7bf7-4eda-8a18-235fbdff37a6[750112f0-7bf7-4eda-8a18-235fbdff37a6] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.738 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.738 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.738 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:48.815 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:48.815 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:48.816 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848737 
[TRACE] 2025-06-20 09:10:48.816 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381848737 
[TRACE] 2025-06-20 09:10:48.816 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.816 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:48.816 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.887 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:48.887 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381848745 
[TRACE] 2025-06-20 09:10:48.887 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381848745 
[TRACE] 2025-06-20 09:10:48.887 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.887 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:48.951 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.951 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:48.952 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e2724e08-18fd-4a15-a48d-7d6bc2313229 
[INFO ] 2025-06-20 09:10:48.953 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e2724e08-18fd-4a15-a48d-7d6bc2313229 
[INFO ] 2025-06-20 09:10:48.953 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.953 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.953 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:48.953 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] running status set to false 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] schema data cleaned 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] monitor closed 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)][c167cef8-0b02-4875-9a9c-ba717de924e5] - Node c167cef8-0b02-4875-9a9c-ba717de924e5[c167cef8-0b02-4875-9a9c-ba717de924e5] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:49.025 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:49.025 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:49.025 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:49.025 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:49.026 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.026 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.026 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.034 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:49.034 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:49.040 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-07c2e67d-a833-4e6c-8028-97cfec897ef4 
[INFO ] 2025-06-20 09:10:49.040 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-07c2e67d-a833-4e6c-8028-97cfec897ef4 
[INFO ] 2025-06-20 09:10:49.040 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.041 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.041 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:49.041 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:49.042 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] running status set to false 
[TRACE] 2025-06-20 09:10:49.042 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] monitor closed 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)][f4e2304d-f29e-4eec-a015-1d1c4974b614] - Node f4e2304d-f29e-4eec-a015-1d1c4974b614[f4e2304d-f29e-4eec-a015-1d1c4974b614] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:49.146 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:49.146 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381849051 
[TRACE] 2025-06-20 09:10:49.146 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381849051 
[TRACE] 2025-06-20 09:10:49.146 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.146 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:49.273 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:49.273 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:49.275 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-d634aef1-89fc-414e-86fc-349bcb23645f 
[INFO ] 2025-06-20 09:10:49.275 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-d634aef1-89fc-414e-86fc-349bcb23645f 
[INFO ] 2025-06-20 09:10:49.276 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.276 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.276 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:49.276 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:49.279 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] running status set to false 
[TRACE] 2025-06-20 09:10:49.279 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] schema data cleaned 
[TRACE] 2025-06-20 09:10:49.279 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] monitor closed 
[TRACE] 2025-06-20 09:10:49.279 - [任务 16(100)][8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] - Node 8d1f1faf-29ba-47cc-959a-ad28bc33e1cd[8d1f1faf-29ba-47cc-959a-ad28bc33e1cd] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.280 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:49.280 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:49.280 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:49.280 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:49.948 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:49.948 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:49.948 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:49.948 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.949 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.949 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:49.949 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381849976 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381849976 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:50.056 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:50.194 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:50.196 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-d8113f04-d189-4247-b1f4-3c073dd023cc 
[INFO ] 2025-06-20 09:10:50.196 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-d8113f04-d189-4247-b1f4-3c073dd023cc 
[INFO ] 2025-06-20 09:10:50.196 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.196 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.197 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:50.197 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] running status set to false 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] monitor closed 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)][81641864-20ba-4b1f-860a-8286d26a17e7] - Node 81641864-20ba-4b1f-860a-8286d26a17e7[81641864-20ba-4b1f-860a-8286d26a17e7] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:50.202 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:50.202 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:50.245 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:50.246 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:50.246 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:50.246 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.246 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.246 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.342 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:50.342 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:50.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381850260 
[TRACE] 2025-06-20 09:10:50.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381850260 
[TRACE] 2025-06-20 09:10:50.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.349 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:50.478 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:50.478 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:50.483 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6beb3590-b067-4de8-8e4e-3ba1b83b5013 
[INFO ] 2025-06-20 09:10:50.483 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6beb3590-b067-4de8-8e4e-3ba1b83b5013 
[INFO ] 2025-06-20 09:10:50.483 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.484 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.484 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] running status set to false 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] monitor closed 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)][4b24cad5-5f95-4661-8b7e-08256308e5a3] - Node 4b24cad5-5f95-4661-8b7e-08256308e5a3[4b24cad5-5f95-4661-8b7e-08256308e5a3] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.550 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.651 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:50.651 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:50.657 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381850565 
[TRACE] 2025-06-20 09:10:50.657 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381850565 
[TRACE] 2025-06-20 09:10:50.657 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.657 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:50.657 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-880782fb-43a2-4963-bc06-2a53e8aab651 
[INFO ] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-880782fb-43a2-4963-bc06-2a53e8aab651 
[INFO ] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:50.810 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:50.814 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] running status set to false 
[TRACE] 2025-06-20 09:10:50.814 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] schema data cleaned 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] monitor closed 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16(100)][effca3ea-5688-49fc-ba83-5281d9cfc766] - Node effca3ea-5688-49fc-ba83-5281d9cfc766[effca3ea-5688-49fc-ba83-5281d9cfc766] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:51.019 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:51.840 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:51.842 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381761754 
[TRACE] 2025-06-20 09:10:51.842 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381761754 
[TRACE] 2025-06-20 09:10:51.842 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:51.842 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:52.006 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:52.006 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a5911622-36c5-4e89-8d39-6287cc488f96 
[INFO ] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a5911622-36c5-4e89-8d39-6287cc488f96 
[INFO ] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:52.007 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] running status set to false 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] monitor closed 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)][052b45ab-e104-4405-a353-17a475addab8] - Node 052b45ab-e104-4405-a353-17a475addab8[052b45ab-e104-4405-a353-17a475addab8] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:52.056 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:52.056 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.056 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:52.057 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.057 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.057 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.057 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.057 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381852073 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381852073 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:52.169 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a61ff03e-b66d-4baf-8473-50dbb3e0f7be 
[INFO ] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a61ff03e-b66d-4baf-8473-50dbb3e0f7be 
[INFO ] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.320 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:52.321 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:52.321 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] running status set to false 
[TRACE] 2025-06-20 09:10:52.321 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.321 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] monitor closed 
[TRACE] 2025-06-20 09:10:52.321 - [任务 16(100)][52d678cb-8146-49c0-9d2c-820df85cc99b] - Node 52d678cb-8146-49c0-9d2c-820df85cc99b[52d678cb-8146-49c0-9d2c-820df85cc99b] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.322 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:52.322 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:52.322 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.380 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381852393 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381852393 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:52.471 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6ea064b8-a543-4272-bcb9-aa2035b63fd2 
[INFO ] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6ea064b8-a543-4272-bcb9-aa2035b63fd2 
[INFO ] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:52.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:52.598 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:52.598 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381762515 
[TRACE] 2025-06-20 09:10:52.598 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381762515 
[TRACE] 2025-06-20 09:10:52.599 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.599 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:52.599 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.602 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] running status set to false 
[TRACE] 2025-06-20 09:10:52.602 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.602 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] monitor closed 
[TRACE] 2025-06-20 09:10:52.602 - [任务 16(100)][078efe4b-6060-472f-8b1e-e44ddc17a111] - Node 078efe4b-6060-472f-8b1e-e44ddc17a111[078efe4b-6060-472f-8b1e-e44ddc17a111] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.603 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:52.603 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:52.603 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:52.603 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-484c839d-586c-4240-b61e-62092b3cee8f 
[INFO ] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-484c839d-586c-4240-b61e-62092b3cee8f 
[INFO ] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:52.723 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:52.729 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] running status set to false 
[TRACE] 2025-06-20 09:10:52.729 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] monitor closed 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)][c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] - Node c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b[c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:52.776 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:52.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:52.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381852790 
[TRACE] 2025-06-20 09:10:52.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381852790 
[TRACE] 2025-06-20 09:10:52.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:52.885 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:53.017 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:53.017 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6f0825b8-8a62-4e4f-aa4b-bb8e7c7728a3 
[INFO ] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6f0825b8-8a62-4e4f-aa4b-bb8e7c7728a3 
[INFO ] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:53.025 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] running status set to false 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] monitor closed 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)][02f73a3a-6c78-4134-9f70-083b345a1a1e] - Node 02f73a3a-6c78-4134-9f70-083b345a1a1e[02f73a3a-6c78-4134-9f70-083b345a1a1e] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:53.085 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381853103 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381853103 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:53.194 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:53.333 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:53.333 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-c4576ab3-c458-4d9c-975b-98724fc9a3f0 
[INFO ] 2025-06-20 09:10:53.333 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-c4576ab3-c458-4d9c-975b-98724fc9a3f0 
[INFO ] 2025-06-20 09:10:53.333 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.334 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.334 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:53.338 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:53.338 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] running status set to false 
[TRACE] 2025-06-20 09:10:53.338 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] schema data cleaned 
[TRACE] 2025-06-20 09:10:53.338 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] monitor closed 
[TRACE] 2025-06-20 09:10:53.338 - [任务 16(100)][c9905877-a6e2-4340-a003-2e85e4798a37] - Node c9905877-a6e2-4340-a003-2e85e4798a37[c9905877-a6e2-4340-a003-2e85e4798a37] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:53.339 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:53.339 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:53.339 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:53.541 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381766213 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381766213 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:56.298 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-63ecb0ab-a399-4dfc-b3de-de96b8b59190 
[INFO ] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-63ecb0ab-a399-4dfc-b3de-de96b8b59190 
[INFO ] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:56.439 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:56.445 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] running status set to false 
[TRACE] 2025-06-20 09:10:56.445 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.445 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] monitor closed 
[TRACE] 2025-06-20 09:10:56.445 - [任务 16(100)][be1cf111-c9fd-4731-b45d-3ba72b4711cd] - Node be1cf111-c9fd-4731-b45d-3ba72b4711cd[be1cf111-c9fd-4731-b45d-3ba72b4711cd] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.446 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:56.446 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:56.446 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:56.489 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:56.489 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:56.490 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.490 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:56.490 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:56.490 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.490 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.592 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:56.592 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:56.598 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381856505 
[TRACE] 2025-06-20 09:10:56.598 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381856505 
[TRACE] 2025-06-20 09:10:56.598 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.598 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:56.598 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:56.732 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[TRACE] 2025-06-20 09:10:56.732 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] running status set to false 
[TRACE] 2025-06-20 09:10:56.732 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.732 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] monitor closed 
[TRACE] 2025-06-20 09:10:56.732 - [任务 16(100)][f61e46d6-fcce-47be-a7fb-ae0175c6e83a] - Node f61e46d6-fcce-47be-a7fb-ae0175c6e83a[f61e46d6-fcce-47be-a7fb-ae0175c6e83a] close complete, cost 0 ms 
[INFO ] 2025-06-20 09:10:56.735 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-5b37dcef-7632-49a8-9422-4d3508be5afc 
[INFO ] 2025-06-20 09:10:56.735 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-5b37dcef-7632-49a8-9422-4d3508be5afc 
[INFO ] 2025-06-20 09:10:56.735 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:56.796 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.854 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:56.854 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:56.855 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381766771 
[TRACE] 2025-06-20 09:10:56.855 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381766771 
[TRACE] 2025-06-20 09:10:56.855 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.855 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:56.899 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:56.899 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:56.907 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381856813 
[TRACE] 2025-06-20 09:10:56.907 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381856813 
[TRACE] 2025-06-20 09:10:56.908 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:56.908 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:56.908 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:10:56.999 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:57.000 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-cb2e2706-b119-470c-bf29-0b442c2d1a89 
[INFO ] 2025-06-20 09:10:57.000 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-cb2e2706-b119-470c-bf29-0b442c2d1a89 
[INFO ] 2025-06-20 09:10:57.000 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.001 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.001 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:57.001 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] running status set to false 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] monitor closed 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)][835add9e-bd79-4bee-a57e-29acbaeac4a4] - Node 835add9e-bd79-4bee-a57e-29acbaeac4a4[835add9e-bd79-4bee-a57e-29acbaeac4a4] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:57.005 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.053 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:57.077 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:57.086 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-dd6a526b-5f7d-4f55-b0f2-1dad5be46951 
[INFO ] 2025-06-20 09:10:57.086 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-dd6a526b-5f7d-4f55-b0f2-1dad5be46951 
[INFO ] 2025-06-20 09:10:57.087 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.087 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.087 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:57.087 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 17 ms 
[TRACE] 2025-06-20 09:10:57.090 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] running status set to false 
[TRACE] 2025-06-20 09:10:57.091 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.091 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] monitor closed 
[TRACE] 2025-06-20 09:10:57.091 - [任务 16(100)][a16759ce-0bdc-4068-b30f-c4e3c50011df] - Node a16759ce-0bdc-4068-b30f-c4e3c50011df[a16759ce-0bdc-4068-b30f-c4e3c50011df] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.094 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:57.094 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:57.094 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:57.094 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:57.183 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:57.183 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381857094 
[TRACE] 2025-06-20 09:10:57.183 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381857094 
[TRACE] 2025-06-20 09:10:57.183 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.184 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:57.184 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:57.323 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:57.323 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-ac89b6ae-3283-45ba-806b-18b80668eea9 
[INFO ] 2025-06-20 09:10:57.323 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-ac89b6ae-3283-45ba-806b-18b80668eea9 
[INFO ] 2025-06-20 09:10:57.323 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] running status set to false 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] monitor closed 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)][1d1876fd-c851-460f-8934-e702d1feed98] - Node 1d1876fd-c851-460f-8934-e702d1feed98[1d1876fd-c851-460f-8934-e702d1feed98] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:57.325 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:57.325 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.384 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.490 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:57.490 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:57.495 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381857401 
[TRACE] 2025-06-20 09:10:57.495 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381857401 
[TRACE] 2025-06-20 09:10:57.495 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.495 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:57.627 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:57.628 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:57.632 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-7d08329a-2a9e-4604-a7e9-16089df7df85 
[INFO ] 2025-06-20 09:10:57.633 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-7d08329a-2a9e-4604-a7e9-16089df7df85 
[INFO ] 2025-06-20 09:10:57.633 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.633 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.633 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:57.633 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] running status set to false 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] schema data cleaned 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] monitor closed 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)][775d43be-0c2e-49fc-aae3-61f35d660ace] - Node 775d43be-0c2e-49fc-aae3-61f35d660ace[775d43be-0c2e-49fc-aae3-61f35d660ace] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:58.093 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:10:58.095 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381768006 
[TRACE] 2025-06-20 09:10:58.095 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381768006 
[TRACE] 2025-06-20 09:10:58.095 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.095 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:10:58.096 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:58.243 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:10:58.243 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-0a862c55-1fd7-4e94-9bab-19b85cc8ec83 
[INFO ] 2025-06-20 09:10:58.243 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-0a862c55-1fd7-4e94-9bab-19b85cc8ec83 
[INFO ] 2025-06-20 09:10:58.244 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.244 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.244 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:10:58.244 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:58.248 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] running status set to false 
[TRACE] 2025-06-20 09:10:58.250 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.250 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] monitor closed 
[TRACE] 2025-06-20 09:10:58.250 - [任务 16(100)][eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] - Node eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d[eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.251 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:58.251 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:58.251 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:58.251 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.295 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.394 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:10:58.394 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:10:58.395 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381858308 
[TRACE] 2025-06-20 09:10:58.395 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381858308 
[TRACE] 2025-06-20 09:10:58.395 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.395 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:10:58.395 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-2ef7cc89-0fd9-475f-a673-82492be7bf95 
[INFO ] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-2ef7cc89-0fd9-475f-a673-82492be7bf95 
[INFO ] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:10:58.518 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:10:58.526 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] running status set to false 
[TRACE] 2025-06-20 09:10:58.526 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.526 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] monitor closed 
[TRACE] 2025-06-20 09:10:58.526 - [任务 16(100)][97b7172d-3468-4ec3-975c-9ab9e37c2cce] - Node 97b7172d-3468-4ec3-975c-9ab9e37c2cce[97b7172d-3468-4ec3-975c-9ab9e37c2cce] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.527 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:58.527 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:58.527 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:58.527 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.591 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.697 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:10:58.697 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:10:58.704 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381858606 
[TRACE] 2025-06-20 09:10:58.704 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381858606 
[TRACE] 2025-06-20 09:10:58.704 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.704 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:10:58.833 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:58.833 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-185512ce-af0c-4816-b84a-942cc2d29c45 
[INFO ] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-185512ce-af0c-4816-b84a-942cc2d29c45 
[INFO ] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:10:58.840 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:10:58.841 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] running status set to false 
[TRACE] 2025-06-20 09:10:58.841 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] schema data cleaned 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] monitor closed 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)][b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] - Node b29141a0-bcf3-49f9-8a3c-70ad1881d0d4[b29141a0-bcf3-49f9-8a3c-70ad1881d0d4] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:14.936 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:14.936 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:14.937 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:14.937 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:14.937 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:14.937 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:14.937 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381874976 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381874976 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:11:15.070 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:11:15.204 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-c227ff47-1a75-44cb-a016-aaffaf27b808 
[INFO ] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-c227ff47-1a75-44cb-a016-aaffaf27b808 
[INFO ] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:11:15.205 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:11:15.210 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] running status set to false 
[TRACE] 2025-06-20 09:11:15.210 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.210 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] monitor closed 
[TRACE] 2025-06-20 09:11:15.210 - [任务 16(100)][dc7aacb9-4b25-416a-be6c-43f766d641b1] - Node dc7aacb9-4b25-416a-be6c-43f766d641b1[dc7aacb9-4b25-416a-be6c-43f766d641b1] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.211 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:15.211 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:15.211 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.255 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.358 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:11:15.358 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:11:15.363 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381875270 
[TRACE] 2025-06-20 09:11:15.363 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381875270 
[TRACE] 2025-06-20 09:11:15.363 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.363 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:11:15.363 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:11:15.508 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:11:15.508 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-bc6c70d1-2961-498b-94ad-c6e9ad8b68b6 
[INFO ] 2025-06-20 09:11:15.509 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-bc6c70d1-2961-498b-94ad-c6e9ad8b68b6 
[INFO ] 2025-06-20 09:11:15.509 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] running status set to false 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] monitor closed 
[TRACE] 2025-06-20 09:11:15.509 - [任务 16(100)][76e8a5f3-d4a8-46f1-920b-dc972481c968] - Node 76e8a5f3-d4a8-46f1-920b-dc972481c968[76e8a5f3-d4a8-46f1-920b-dc972481c968] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.510 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:15.510 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:15.510 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:15.510 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:15.569 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:15.570 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:15.570 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:15.570 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.570 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.570 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 1 ms 
[TRACE] 2025-06-20 09:11:15.674 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:11:15.675 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:11:15.684 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381875584 
[TRACE] 2025-06-20 09:11:15.684 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381875584 
[TRACE] 2025-06-20 09:11:15.684 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.684 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:11:15.684 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 9 ms 
[TRACE] 2025-06-20 09:11:15.818 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-9ea4ede8-4591-4210-877e-570e7e2fc2ed 
[INFO ] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-9ea4ede8-4591-4210-877e-570e7e2fc2ed 
[INFO ] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:11:15.823 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] running status set to false 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] schema data cleaned 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] monitor closed 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)][5f5f939f-ea86-45b6-be85-5662b008017d] - Node 5f5f939f-ea86-45b6-be85-5662b008017d[5f5f939f-ea86-45b6-be85-5662b008017d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:16.030 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.223 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.330 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:11:17.331 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:11:17.334 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381877239 
[TRACE] 2025-06-20 09:11:17.334 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381877239 
[TRACE] 2025-06-20 09:11:17.334 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.334 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:11:17.334 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:11:17.473 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:11:17.473 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-f8d9fea4-d37d-4e04-9d10-baacaf55cf2e 
[INFO ] 2025-06-20 09:11:17.473 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-f8d9fea4-d37d-4e04-9d10-baacaf55cf2e 
[INFO ] 2025-06-20 09:11:17.473 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.473 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.474 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:11:17.474 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:11:17.481 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] running status set to false 
[TRACE] 2025-06-20 09:11:17.481 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.481 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] monitor closed 
[TRACE] 2025-06-20 09:11:17.482 - [任务 16(100)][f045c2eb-c101-43cb-bae1-5d7bea5834ba] - Node f045c2eb-c101-43cb-bae1-5d7bea5834ba[f045c2eb-c101-43cb-bae1-5d7bea5834ba] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.482 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:17.482 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:17.482 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.527 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:11:17.646 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:11:17.646 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381877545 
[TRACE] 2025-06-20 09:11:17.646 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381877545 
[TRACE] 2025-06-20 09:11:17.646 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.646 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:11:17.775 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:11:17.776 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:11:17.782 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-70515a52-4b2a-41ec-99c5-ab3e26abcc8c 
[INFO ] 2025-06-20 09:11:17.782 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-70515a52-4b2a-41ec-99c5-ab3e26abcc8c 
[INFO ] 2025-06-20 09:11:17.782 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] running status set to false 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] monitor closed 
[TRACE] 2025-06-20 09:11:17.783 - [任务 16(100)][2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] - Node 2ec6d79c-8ad5-476a-8aec-25e6d2b3a129[2ec6d79c-8ad5-476a-8aec-25e6d2b3a129] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.784 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:17.784 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:17.784 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:17.784 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:17.844 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:11:17.955 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:11:17.956 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381877860 
[TRACE] 2025-06-20 09:11:17.956 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381877860 
[TRACE] 2025-06-20 09:11:17.956 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:11:17.956 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:11:18.093 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:11:18.093 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:11:18.097 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-da039066-f573-45e0-897b-8f336ca16188 
[INFO ] 2025-06-20 09:11:18.097 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-da039066-f573-45e0-897b-8f336ca16188 
[INFO ] 2025-06-20 09:11:18.097 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:11:18.098 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:11:18.098 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:11:18.098 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:11:18.099 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] running status set to false 
[TRACE] 2025-06-20 09:11:18.099 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] schema data cleaned 
[TRACE] 2025-06-20 09:11:18.099 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] monitor closed 
[TRACE] 2025-06-20 09:11:18.099 - [任务 16(100)][5e9baf57-d99e-4fd6-8940-210f3cae6712] - Node 5e9baf57-d99e-4fd6-8940-210f3cae6712[5e9baf57-d99e-4fd6-8940-210f3cae6712] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.100 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:11:18.100 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:11:18.100 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:11:18.100 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:56.935 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:56.936 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:56.936 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:56.936 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:56.936 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:56.936 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:56.980 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:56.981 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.186 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382577088 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382577088 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:57.202 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:22:57.254 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:57.254 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382577095 
[TRACE] 2025-06-20 09:22:57.255 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382577095 
[TRACE] 2025-06-20 09:22:57.255 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.255 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:57.255 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:22:57.383 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:22:57.383 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-0ad418c8-3c42-49e3-859f-0f2cb6fba2c2 
[INFO ] 2025-06-20 09:22:57.383 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-0ad418c8-3c42-49e3-859f-0f2cb6fba2c2 
[INFO ] 2025-06-20 09:22:57.383 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.384 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.384 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:22:57.384 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:57.396 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] running status set to false 
[TRACE] 2025-06-20 09:22:57.396 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.396 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] monitor closed 
[TRACE] 2025-06-20 09:22:57.396 - [任务 16(100)][cd331c43-862f-4863-beed-6ba2dac15c57] - Node cd331c43-862f-4863-beed-6ba2dac15c57[cd331c43-862f-4863-beed-6ba2dac15c57] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.397 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:57.397 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:57.397 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:57.449 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:57.450 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:22:57.450 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-9a2290af-a894-40e8-abe0-ac8964c8af64 
[INFO ] 2025-06-20 09:22:57.450 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-9a2290af-a894-40e8-abe0-ac8964c8af64 
[INFO ] 2025-06-20 09:22:57.450 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.451 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.451 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:22:57.451 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:22:57.462 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] running status set to false 
[TRACE] 2025-06-20 09:22:57.462 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.462 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] monitor closed 
[TRACE] 2025-06-20 09:22:57.462 - [任务 16(100)][9d5ccb6b-111b-491e-b795-c4a281ed3150] - Node 9d5ccb6b-111b-491e-b795-c4a281ed3150[9d5ccb6b-111b-491e-b795-c4a281ed3150] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.463 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:57.463 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:57.463 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:57.480 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:57.480 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:57.480 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:57.481 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:57.481 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.481 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.485 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.485 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:22:57.511 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.512 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:22:57.722 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:22:57.727 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382577621 
[TRACE] 2025-06-20 09:22:57.727 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382577621 
[TRACE] 2025-06-20 09:22:57.728 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.728 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:22:57.728 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:22:57.779 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:22:57.779 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382577627 
[TRACE] 2025-06-20 09:22:57.780 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382577627 
[TRACE] 2025-06-20 09:22:57.780 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.780 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:22:57.780 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.910 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:22:57.910 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-8e67816e-6fcf-4e91-bead-a4cedcfd3f08 
[INFO ] 2025-06-20 09:22:57.910 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-8e67816e-6fcf-4e91-bead-a4cedcfd3f08 
[INFO ] 2025-06-20 09:22:57.910 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.911 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.911 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:22:57.911 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:22:57.916 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] running status set to false 
[TRACE] 2025-06-20 09:22:57.916 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.916 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] monitor closed 
[TRACE] 2025-06-20 09:22:57.916 - [任务 16(100)][3dc0d96c-af08-4138-9bcc-b6dca0d70551] - Node 3dc0d96c-af08-4138-9bcc-b6dca0d70551[3dc0d96c-af08-4138-9bcc-b6dca0d70551] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.917 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:57.917 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:57.917 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:57.917 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:57.976 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:22:57.976 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-f2cd19be-3f78-42df-b9bb-9cdd9242d7c7 
[INFO ] 2025-06-20 09:22:57.976 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-f2cd19be-3f78-42df-b9bb-9cdd9242d7c7 
[INFO ] 2025-06-20 09:22:57.978 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.978 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.978 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:22:57.978 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:57.983 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] running status set to false 
[TRACE] 2025-06-20 09:22:57.983 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] schema data cleaned 
[TRACE] 2025-06-20 09:22:57.983 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] monitor closed 
[TRACE] 2025-06-20 09:22:57.983 - [任务 16(100)][0d9427b8-5646-4ff6-990f-2ba3585509df] - Node 0d9427b8-5646-4ff6-990f-2ba3585509df[0d9427b8-5646-4ff6-990f-2ba3585509df] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:57.984 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:57.984 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:57.984 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:58.022 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:58.022 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.022 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:58.022 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.022 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.023 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.023 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.023 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:22:58.058 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.058 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:58.058 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.058 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.062 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.062 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.062 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:22:58.281 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:22:58.281 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382578169 
[TRACE] 2025-06-20 09:22:58.282 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382578169 
[TRACE] 2025-06-20 09:22:58.282 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.282 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:22:58.282 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382578177 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382578177 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:22:58.323 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:22:58.431 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:22:58.431 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1275824e-2384-46da-b85e-7928f4ecd71a 
[INFO ] 2025-06-20 09:22:58.431 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1275824e-2384-46da-b85e-7928f4ecd71a 
[INFO ] 2025-06-20 09:22:58.431 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.432 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.432 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:22:58.435 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:22:58.435 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] running status set to false 
[TRACE] 2025-06-20 09:22:58.435 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.435 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] monitor closed 
[TRACE] 2025-06-20 09:22:58.436 - [任务 16(100)][e334b66d-132b-4c2d-9888-8c62a06358c9] - Node e334b66d-132b-4c2d-9888-8c62a06358c9[e334b66d-132b-4c2d-9888-8c62a06358c9] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.436 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:58.436 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:58.436 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:58.500 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:58.500 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1587101a-e2c2-4e48-80db-f972ad0af691 
[INFO ] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1587101a-e2c2-4e48-80db-f972ad0af691 
[INFO ] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:22:58.501 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] running status set to false 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] monitor closed 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)][002a5872-e8b5-4196-b2b9-15a0808a28bc] - Node 002a5872-e8b5-4196-b2b9-15a0808a28bc[002a5872-e8b5-4196-b2b9-15a0808a28bc] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:58.713 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.805 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:58.908 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:22:58.908 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:58.910 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382578820 
[TRACE] 2025-06-20 09:22:58.910 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382578820 
[TRACE] 2025-06-20 09:22:58.910 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:58.910 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:58.910 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.054 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:22:59.054 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a1b77922-3229-4854-b91e-aeb895b42ca8 
[INFO ] 2025-06-20 09:22:59.054 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a1b77922-3229-4854-b91e-aeb895b42ca8 
[INFO ] 2025-06-20 09:22:59.054 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.055 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.055 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:22:59.055 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.061 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] running status set to false 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] monitor closed 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)][7d64c490-b4b8-468a-9416-2582b899acae] - Node 7d64c490-b4b8-468a-9416-2582b899acae[7d64c490-b4b8-468a-9416-2582b899acae] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.108 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.138 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.139 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.139 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382579126 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382579126 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:22:59.222 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:22:59.287 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:59.287 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382579155 
[TRACE] 2025-06-20 09:22:59.287 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382579155 
[TRACE] 2025-06-20 09:22:59.287 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.287 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:59.288 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.380 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:22:59.380 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-3c180951-ffee-48ae-9dfa-7f681f351660 
[INFO ] 2025-06-20 09:22:59.380 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-3c180951-ffee-48ae-9dfa-7f681f351660 
[INFO ] 2025-06-20 09:22:59.380 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:22:59.381 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] running status set to false 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] monitor closed 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)][2484d160-afdc-4024-af13-8a8f5ed10ee1] - Node 2484d160-afdc-4024-af13-8a8f5ed10ee1[2484d160-afdc-4024-af13-8a8f5ed10ee1] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:59.447 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:22:59.448 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-9247fec0-2649-4517-a8f3-8e9dcdef36d0 
[INFO ] 2025-06-20 09:22:59.448 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-9247fec0-2649-4517-a8f3-8e9dcdef36d0 
[INFO ] 2025-06-20 09:22:59.448 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.449 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.449 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:22:59.449 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] running status set to false 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] monitor closed 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)][e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] - Node e7a9caf8-5a63-463e-bd20-ce3772ec9bb4[e7a9caf8-5a63-463e-bd20-ce3772ec9bb4] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:59.455 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:59.455 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:59.470 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.470 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.470 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.470 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.475 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.475 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.475 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:22:59.501 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.502 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:22:59.581 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:22:59.582 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382579488 
[TRACE] 2025-06-20 09:22:59.582 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382579488 
[TRACE] 2025-06-20 09:22:59.582 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.582 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:22:59.582 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382579518 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382579518 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:22:59.645 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.660 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.660 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.660 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.660 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.660 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.661 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.661 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:22:59.734 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:22:59.734 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-502b0db6-826f-40cc-b1b3-d0d400c991fb 
[INFO ] 2025-06-20 09:22:59.734 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-502b0db6-826f-40cc-b1b3-d0d400c991fb 
[INFO ] 2025-06-20 09:22:59.734 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.735 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.735 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:22:59.735 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.740 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] running status set to false 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] monitor closed 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)][f7a6f251-17b5-48d4-85c3-69f0f972abe6] - Node f7a6f251-17b5-48d4-85c3-69f0f972abe6[f7a6f251-17b5-48d4-85c3-69f0f972abe6] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:59.802 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:22:59.802 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-0317025a-6001-4d79-84c5-a2f820dafa31 
[INFO ] 2025-06-20 09:22:59.802 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-0317025a-6001-4d79-84c5-a2f820dafa31 
[INFO ] 2025-06-20 09:22:59.802 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.803 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.803 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:22:59.803 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:59.810 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] running status set to false 
[TRACE] 2025-06-20 09:22:59.810 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.810 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] monitor closed 
[TRACE] 2025-06-20 09:22:59.810 - [任务 16(100)][d91d4e7e-173c-41a8-a150-d1a29991167b] - Node d91d4e7e-173c-41a8-a150-d1a29991167b[d91d4e7e-173c-41a8-a150-d1a29991167b] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.811 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:22:59.811 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:22:59.811 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:22:59.811 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:59.873 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:59.873 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382579675 
[TRACE] 2025-06-20 09:22:59.873 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382579675 
[TRACE] 2025-06-20 09:22:59.873 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:59.874 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:59.874 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:22:59.889 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.040 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:00.040 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:23:00.048 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382579945 
[TRACE] 2025-06-20 09:23:00.048 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382579945 
[TRACE] 2025-06-20 09:23:00.048 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.048 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:23:00.048 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:23:00.119 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:23:00.121 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-370b1e22-7d57-4025-affd-bc037cc073a1 
[INFO ] 2025-06-20 09:23:00.121 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-370b1e22-7d57-4025-affd-bc037cc073a1 
[INFO ] 2025-06-20 09:23:00.121 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.122 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.122 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] running status set to false 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] monitor closed 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)][de36aa1b-0280-431a-a5b8-9bb04368807c] - Node de36aa1b-0280-431a-a5b8-9bb04368807c[de36aa1b-0280-431a-a5b8-9bb04368807c] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:00.131 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:00.175 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:00.175 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:00.175 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:00.175 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.175 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.176 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.184 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:23:00.184 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:23:00.189 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-780cf317-68ec-4f70-9d71-388e7c9bed66 
[INFO ] 2025-06-20 09:23:00.190 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-780cf317-68ec-4f70-9d71-388e7c9bed66 
[INFO ] 2025-06-20 09:23:00.190 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.190 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.190 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:23:00.190 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:23:00.194 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] running status set to false 
[TRACE] 2025-06-20 09:23:00.194 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.194 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] monitor closed 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16(100)][d1a87780-fb64-40ec-98ba-349bf1c61705] - Node d1a87780-fb64-40ec-98ba-349bf1c61705[d1a87780-fb64-40ec-98ba-349bf1c61705] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:00.279 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:23:00.281 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382580201 
[TRACE] 2025-06-20 09:23:00.281 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382580201 
[TRACE] 2025-06-20 09:23:00.281 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.281 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:23:00.281 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:00.416 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:23:00.418 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-83d3c5ca-0a70-4563-8329-50a2a33c755e 
[INFO ] 2025-06-20 09:23:00.418 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-83d3c5ca-0a70-4563-8329-50a2a33c755e 
[INFO ] 2025-06-20 09:23:00.418 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.419 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.419 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:23:00.419 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] running status set to false 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] monitor closed 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)][ba445fde-1b3c-4457-86e7-289caead96da] - Node ba445fde-1b3c-4457-86e7-289caead96da[ba445fde-1b3c-4457-86e7-289caead96da] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.487 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:00.589 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:00.589 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:23:00.593 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382580503 
[TRACE] 2025-06-20 09:23:00.595 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382580503 
[TRACE] 2025-06-20 09:23:00.595 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.595 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:23:00.595 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:23:00.744 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[TRACE] 2025-06-20 09:23:00.744 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] running status set to false 
[TRACE] 2025-06-20 09:23:00.744 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.744 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] monitor closed 
[INFO ] 2025-06-20 09:23:00.744 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-96e45cf9-1a37-44da-8604-206efc649c36 
[TRACE] 2025-06-20 09:23:00.744 - [任务 16(100)][2f35fb91-a410-4606-949f-7cd8e69496cb] - Node 2f35fb91-a410-4606-949f-7cd8e69496cb[2f35fb91-a410-4606-949f-7cd8e69496cb] close complete, cost 0 ms 
[INFO ] 2025-06-20 09:23:00.744 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-96e45cf9-1a37-44da-8604-206efc649c36 
[INFO ] 2025-06-20 09:23:00.744 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.745 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:23:00.745 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:23:00.745 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:23:00.745 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:00.746 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:00.746 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:00.951 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:09.807 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:09.808 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382589824 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382589824 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:23:09.921 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:23:10.053 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:23:10.053 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-87b8d8bc-96f9-44c5-9814-7340d99db498 
[INFO ] 2025-06-20 09:23:10.054 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-87b8d8bc-96f9-44c5-9814-7340d99db498 
[INFO ] 2025-06-20 09:23:10.054 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.054 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.054 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:23:10.054 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:10.059 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] running status set to false 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] monitor closed 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)][3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] - Node 3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56[3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.107 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.208 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:23:10.208 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:23:10.216 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382590122 
[TRACE] 2025-06-20 09:23:10.216 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382590122 
[TRACE] 2025-06-20 09:23:10.216 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.216 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:23:10.352 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:23:10.352 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:23:10.361 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-36c1cca5-f058-4b13-9987-a6438301833d 
[INFO ] 2025-06-20 09:23:10.361 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-36c1cca5-f058-4b13-9987-a6438301833d 
[INFO ] 2025-06-20 09:23:10.362 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.366 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.367 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:23:10.367 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 19 ms 
[TRACE] 2025-06-20 09:23:10.367 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] running status set to false 
[TRACE] 2025-06-20 09:23:10.368 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.368 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] monitor closed 
[TRACE] 2025-06-20 09:23:10.368 - [任务 16(100)][0bb7dead-d4b2-4b45-b58b-498323a269ff] - Node 0bb7dead-d4b2-4b45-b58b-498323a269ff[0bb7dead-d4b2-4b45-b58b-498323a269ff] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:23:10.370 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:10.370 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:10.371 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:10.371 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:10.468 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:10.468 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:10.468 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:10.468 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.468 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.469 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.469 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:10.585 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:23:10.585 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382590483 
[TRACE] 2025-06-20 09:23:10.585 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382590483 
[TRACE] 2025-06-20 09:23:10.585 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.585 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:23:10.717 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:23:10.717 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:23:10.719 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-123f3238-3a56-41cd-ba7b-eaf06deda21a 
[INFO ] 2025-06-20 09:23:10.719 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-123f3238-3a56-41cd-ba7b-eaf06deda21a 
[INFO ] 2025-06-20 09:23:10.720 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.720 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.720 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:23:10.720 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:23:10.723 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] running status set to false 
[TRACE] 2025-06-20 09:23:10.723 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] schema data cleaned 
[TRACE] 2025-06-20 09:23:10.723 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] monitor closed 
[TRACE] 2025-06-20 09:23:10.724 - [任务 16(100)][079d072f-a931-49ba-98e8-790dc323937a] - Node 079d072f-a931-49ba-98e8-790dc323937a[079d072f-a931-49ba-98e8-790dc323937a] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:10.724 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:10.724 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:10.724 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:10.928 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:11.780 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:11.781 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382591798 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382591798 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:23:11.884 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:12.034 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:23:12.034 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1a4cd14b-65af-4c4e-85dd-fb0610be346b 
[INFO ] 2025-06-20 09:23:12.034 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1a4cd14b-65af-4c4e-85dd-fb0610be346b 
[INFO ] 2025-06-20 09:23:12.034 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.035 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.035 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:23:12.035 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:12.044 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] running status set to false 
[TRACE] 2025-06-20 09:23:12.044 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.044 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] monitor closed 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16(100)][3ab21b55-8a83-43fd-a2c6-076f784b8a0e] - Node 3ab21b55-8a83-43fd-a2c6-076f784b8a0e[3ab21b55-8a83-43fd-a2c6-076f784b8a0e] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.093 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.199 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:23:12.199 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:23:12.206 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382592109 
[TRACE] 2025-06-20 09:23:12.207 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382592109 
[TRACE] 2025-06-20 09:23:12.207 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.207 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:23:12.207 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:23:12.343 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:23:12.348 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-eb2a8332-4eee-4baf-b4f6-19cb6d361589 
[INFO ] 2025-06-20 09:23:12.349 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-eb2a8332-4eee-4baf-b4f6-19cb6d361589 
[INFO ] 2025-06-20 09:23:12.349 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.349 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.349 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:23:12.349 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:23:12.353 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] running status set to false 
[TRACE] 2025-06-20 09:23:12.353 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.353 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] monitor closed 
[TRACE] 2025-06-20 09:23:12.353 - [任务 16(100)][c1618775-2b45-4354-bf08-db00bb601b74] - Node c1618775-2b45-4354-bf08-db00bb601b74[c1618775-2b45-4354-bf08-db00bb601b74] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.354 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:12.354 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:12.354 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:12.354 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.417 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.520 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:12.520 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:23:12.532 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382592434 
[TRACE] 2025-06-20 09:23:12.532 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382592434 
[TRACE] 2025-06-20 09:23:12.532 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.533 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:23:12.663 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 12 ms 
[TRACE] 2025-06-20 09:23:12.663 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:23:12.669 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-c476760e-f699-4f6f-99fa-ea4c8eab8c2d 
[INFO ] 2025-06-20 09:23:12.669 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-c476760e-f699-4f6f-99fa-ea4c8eab8c2d 
[INFO ] 2025-06-20 09:23:12.669 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.669 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] running status set to false 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] schema data cleaned 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] monitor closed 
[TRACE] 2025-06-20 09:23:12.670 - [任务 16(100)][1a66752e-beb3-46b0-8b12-077313f11395] - Node 1a66752e-beb3-46b0-8b12-077313f11395[1a66752e-beb3-46b0-8b12-077313f11395] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:12.671 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:12.671 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:12.671 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:12.874 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:15.229 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.230 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382595245 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382595245 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:23:15.339 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:23:15.474 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:23:15.474 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-64b7886d-0e76-4779-9961-25d0d52daa85 
[INFO ] 2025-06-20 09:23:15.474 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-64b7886d-0e76-4779-9961-25d0d52daa85 
[INFO ] 2025-06-20 09:23:15.474 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.475 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.475 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:23:15.475 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:23:15.482 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] running status set to false 
[TRACE] 2025-06-20 09:23:15.482 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] monitor closed 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)][5da2471e-58bc-4a34-a517-f6927562c86a] - Node 5da2471e-58bc-4a34-a517-f6927562c86a[5da2471e-58bc-4a34-a517-f6927562c86a] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:15.531 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.531 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.531 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:15.532 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.532 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.532 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.532 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382595551 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382595551 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:23:15.647 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:23:15.789 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:23:15.789 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b21480db-0ae1-45c7-b27e-94adb8371cbc 
[INFO ] 2025-06-20 09:23:15.789 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b21480db-0ae1-45c7-b27e-94adb8371cbc 
[INFO ] 2025-06-20 09:23:15.789 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.790 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.790 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:23:15.790 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:23:15.791 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] running status set to false 
[TRACE] 2025-06-20 09:23:15.791 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.791 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] monitor closed 
[TRACE] 2025-06-20 09:23:15.791 - [任务 16(100)][933aa508-d092-4bd2-baba-5ad1183ceb62] - Node 933aa508-d092-4bd2-baba-5ad1183ceb62[933aa508-d092-4bd2-baba-5ad1183ceb62] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.792 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:15.792 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:15.792 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:15.792 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:15.853 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:15.985 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:23:15.985 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382595872 
[TRACE] 2025-06-20 09:23:15.985 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382595872 
[TRACE] 2025-06-20 09:23:15.985 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:23:15.985 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:23:16.128 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:23:16.128 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:23:16.132 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-5e4b4afd-42bb-4fb4-a254-0cc2334a5709 
[INFO ] 2025-06-20 09:23:16.132 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-5e4b4afd-42bb-4fb4-a254-0cc2334a5709 
[INFO ] 2025-06-20 09:23:16.132 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:23:16.133 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:23:16.133 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:23:16.133 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] running status set to false 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] schema data cleaned 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] monitor closed 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)][d46605bc-26bf-468a-94fa-a910ab5d0e05] - Node d46605bc-26bf-468a-94fa-a910ab5d0e05[d46605bc-26bf-468a-94fa-a910ab5d0e05] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:23:16.192 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:12.460 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.461 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382772479 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382772479 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:26:12.574 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:26:12.702 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:26:12.702 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-65d7ed98-836e-46b2-9fb5-0e3c906e3f51 
[INFO ] 2025-06-20 09:26:12.703 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-65d7ed98-836e-46b2-9fb5-0e3c906e3f51 
[INFO ] 2025-06-20 09:26:12.703 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:12.703 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:26:12.703 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:26:12.703 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:26:12.709 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] running status set to false 
[TRACE] 2025-06-20 09:26:12.709 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] schema data cleaned 
[TRACE] 2025-06-20 09:26:12.709 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] monitor closed 
[TRACE] 2025-06-20 09:26:12.709 - [任务 16(100)][bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] - Node bbb934c3-d982-41b4-af7c-aeb1b38d5b2d[bbb934c3-d982-41b4-af7c-aeb1b38d5b2d] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.710 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:12.710 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:12.710 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:12.710 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.760 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:12.861 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:26:12.861 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:26:12.864 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382772780 
[TRACE] 2025-06-20 09:26:12.864 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382772780 
[TRACE] 2025-06-20 09:26:12.864 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:26:12.865 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:26:12.865 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:26:13.045 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[TRACE] 2025-06-20 09:26:13.056 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] running status set to false 
[TRACE] 2025-06-20 09:26:13.057 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.057 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] monitor closed 
[TRACE] 2025-06-20 09:26:13.057 - [任务 16(100)][55652850-ea3d-4694-9679-380444315742] - Node 55652850-ea3d-4694-9679-380444315742[55652850-ea3d-4694-9679-380444315742] close complete, cost 0 ms 
[INFO ] 2025-06-20 09:26:13.057 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-48440005-609b-434f-bf95-446d6cf5dfdf 
[INFO ] 2025-06-20 09:26:13.058 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-48440005-609b-434f-bf95-446d6cf5dfdf 
[INFO ] 2025-06-20 09:26:13.058 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.058 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.058 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:26:13.058 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 13 ms 
[TRACE] 2025-06-20 09:26:13.059 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:13.059 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:13.059 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:13.059 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:13.142 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382773163 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382773163 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:26:13.257 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e4acf681-acc3-43f3-b1a9-9bdbb0c15f18 
[INFO ] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e4acf681-acc3-43f3-b1a9-9bdbb0c15f18 
[INFO ] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.400 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:26:13.404 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:26:13.404 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] running status set to false 
[TRACE] 2025-06-20 09:26:13.404 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] schema data cleaned 
[TRACE] 2025-06-20 09:26:13.404 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] monitor closed 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16(100)][02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] - Node 02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2[02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:43.717 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:43.824 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:26:43.824 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:26:43.829 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382803734 
[TRACE] 2025-06-20 09:26:43.829 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382803734 
[TRACE] 2025-06-20 09:26:43.830 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:26:43.830 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:26:43.830 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-ffb7980c-6e46-4d72-af7b-e18be7d6d15e 
[INFO ] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-ffb7980c-6e46-4d72-af7b-e18be7d6d15e 
[INFO ] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:26:43.963 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] running status set to false 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] schema data cleaned 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] monitor closed 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16(100)][5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] - Node 5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3[5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:26:43.972 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:43.972 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:43.972 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:43.972 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:44.018 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:44.019 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:44.019 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:44.019 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.019 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.019 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.118 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:26:44.118 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:26:44.122 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382804034 
[TRACE] 2025-06-20 09:26:44.122 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382804034 
[TRACE] 2025-06-20 09:26:44.122 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.122 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:26:44.255 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:26:44.255 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-3578bc60-1e43-41f7-8806-252e6040ac75 
[INFO ] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-3578bc60-1e43-41f7-8806-252e6040ac75 
[INFO ] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:26:44.259 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:26:44.262 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] running status set to false 
[TRACE] 2025-06-20 09:26:44.262 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] monitor closed 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)][08ce2bc0-7b7a-4d14-a056-127cc0098b38] - Node 08ce2bc0-7b7a-4d14-a056-127cc0098b38[08ce2bc0-7b7a-4d14-a056-127cc0098b38] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.328 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:26:44.440 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:26:44.446 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382804356 
[TRACE] 2025-06-20 09:26:44.446 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382804356 
[TRACE] 2025-06-20 09:26:44.446 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.446 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:26:44.446 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[WARN ] 2025-06-20 09:26:44.581 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:26:44.584 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a6b33576-bdd9-4c7d-8d4c-86c53025b160 
[INFO ] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a6b33576-bdd9-4c7d-8d4c-86c53025b160 
[INFO ] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:26:44.585 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] running status set to false 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] schema data cleaned 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] monitor closed 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)][1e6363a4-89ce-4fde-b383-3db2fb094970] - Node 1e6363a4-89ce-4fde-b383-3db2fb094970[1e6363a4-89ce-4fde-b383-3db2fb094970] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:26:44.791 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:04.203 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.204 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:27:04.314 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:27:04.314 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382824220 
[TRACE] 2025-06-20 09:27:04.314 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382824220 
[TRACE] 2025-06-20 09:27:04.314 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.314 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:27:04.453 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:27:04.453 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:27:04.454 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b94661ea-8d50-4af4-9688-42989c565a21 
[INFO ] 2025-06-20 09:27:04.454 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b94661ea-8d50-4af4-9688-42989c565a21 
[INFO ] 2025-06-20 09:27:04.454 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.455 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.455 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:27:04.455 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:27:04.462 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] running status set to false 
[TRACE] 2025-06-20 09:27:04.462 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.462 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] monitor closed 
[TRACE] 2025-06-20 09:27:04.462 - [任务 16(100)][f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] - Node f3bc618d-626c-4c3c-aca3-0bacdf45d3b0[f3bc618d-626c-4c3c-aca3-0bacdf45d3b0] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.463 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:04.463 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:04.463 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:04.463 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:04.511 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.511 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:04.511 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.511 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.511 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.512 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.512 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382824529 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382824529 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:27:04.622 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:04.761 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:27:04.761 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-914a11f5-dc44-4b25-91b2-3b5eead1a750 
[INFO ] 2025-06-20 09:27:04.761 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-914a11f5-dc44-4b25-91b2-3b5eead1a750 
[INFO ] 2025-06-20 09:27:04.761 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.762 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.762 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:27:04.762 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] running status set to false 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] monitor closed 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16(100)][b501d238-53c9-4adf-a4c6-335672f6b729] - Node b501d238-53c9-4adf-a4c6-335672f6b729[b501d238-53c9-4adf-a4c6-335672f6b729] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:04.767 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:04.767 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.825 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:04.928 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:27:04.929 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:27:04.935 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382824840 
[TRACE] 2025-06-20 09:27:04.935 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382824840 
[TRACE] 2025-06-20 09:27:04.935 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:27:04.935 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:27:04.935 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 7 ms 
[WARN ] 2025-06-20 09:27:05.065 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:27:05.065 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:27:05.069 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-9f3f3b8a-d280-4a70-bf42-495c1234c934 
[INFO ] 2025-06-20 09:27:05.069 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-9f3f3b8a-d280-4a70-bf42-495c1234c934 
[INFO ] 2025-06-20 09:27:05.070 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:05.070 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:27:05.070 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:27:05.070 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] running status set to false 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] schema data cleaned 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] monitor closed 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)][b1b44447-d62f-438f-9a8e-a1d3e09c7845] - Node b1b44447-d62f-438f-9a8e-a1d3e09c7845[b1b44447-d62f-438f-9a8e-a1d3e09c7845] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:05.075 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:05.280 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:06.769 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:06.770 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:06.770 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:06.770 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:06.770 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:06.770 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:06.903 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:27:06.903 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:27:06.907 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382826785 
[TRACE] 2025-06-20 09:27:06.907 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382826785 
[TRACE] 2025-06-20 09:27:06.907 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:27:06.907 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:27:06.907 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:27:07.042 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:27:07.042 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-dbf04939-b11b-417a-97be-d1c7afd9af3a 
[INFO ] 2025-06-20 09:27:07.042 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-dbf04939-b11b-417a-97be-d1c7afd9af3a 
[INFO ] 2025-06-20 09:27:07.042 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.043 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.043 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] running status set to false 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] monitor closed 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)][945791c5-41e3-48e9-8ddb-7009f72ffb2c] - Node 945791c5-41e3-48e9-8ddb-7009f72ffb2c[945791c5-41e3-48e9-8ddb-7009f72ffb2c] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.094 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.192 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:27:07.192 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:27:07.201 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382827108 
[TRACE] 2025-06-20 09:27:07.201 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382827108 
[TRACE] 2025-06-20 09:27:07.201 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.201 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:27:07.201 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 9 ms 
[TRACE] 2025-06-20 09:27:07.335 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:27:07.335 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-dc2bcb9c-16b4-473d-8402-9621d021b7a0 
[INFO ] 2025-06-20 09:27:07.335 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-dc2bcb9c-16b4-473d-8402-9621d021b7a0 
[INFO ] 2025-06-20 09:27:07.335 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.336 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.336 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:27:07.336 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:07.339 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] running status set to false 
[TRACE] 2025-06-20 09:27:07.339 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.339 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] monitor closed 
[TRACE] 2025-06-20 09:27:07.339 - [任务 16(100)][6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] - Node 6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4[6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.340 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:07.340 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:07.340 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:07.399 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:07.399 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:07.399 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:07.400 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:07.400 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.400 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.400 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.400 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:27:07.501 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:27:07.508 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382827416 
[TRACE] 2025-06-20 09:27:07.508 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382827416 
[TRACE] 2025-06-20 09:27:07.508 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.508 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:27:07.508 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[WARN ] 2025-06-20 09:27:07.637 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:27:07.637 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:27:07.640 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-2d7fe97c-a5fe-4290-91f3-e81c675dda99 
[INFO ] 2025-06-20 09:27:07.641 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-2d7fe97c-a5fe-4290-91f3-e81c675dda99 
[INFO ] 2025-06-20 09:27:07.641 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.641 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.641 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:27:07.641 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] running status set to false 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] schema data cleaned 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] monitor closed 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)][3901342c-34b1-4756-bba8-1c3e41799028] - Node 3901342c-34b1-4756-bba8-1c3e41799028[3901342c-34b1-4756-bba8-1c3e41799028] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:07.852 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.226 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.227 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382830239 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382830239 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:27:10.329 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:27:10.459 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:27:10.459 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-7ac38b8d-d6fc-4abb-a0f6-5e3e082db568 
[INFO ] 2025-06-20 09:27:10.459 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-7ac38b8d-d6fc-4abb-a0f6-5e3e082db568 
[INFO ] 2025-06-20 09:27:10.459 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.460 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.460 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] running status set to false 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] monitor closed 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)][7aef4af3-fa87-454d-a3cd-39226ff2f2b6] - Node 7aef4af3-fa87-454d-a3cd-39226ff2f2b6[7aef4af3-fa87-454d-a3cd-39226ff2f2b6] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:10.467 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:10.467 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:10.467 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:10.511 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:10.511 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.511 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.512 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.512 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.512 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.634 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:27:10.634 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:27:10.640 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382830526 
[TRACE] 2025-06-20 09:27:10.640 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382830526 
[TRACE] 2025-06-20 09:27:10.640 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.640 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:27:10.641 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:27:10.780 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:27:10.784 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6348cb78-2156-4e82-9382-6b1e62f60fd8 
[INFO ] 2025-06-20 09:27:10.784 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6348cb78-2156-4e82-9382-6b1e62f60fd8 
[INFO ] 2025-06-20 09:27:10.784 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.784 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.785 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:27:10.785 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:27:10.789 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] running status set to false 
[TRACE] 2025-06-20 09:27:10.789 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.789 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] monitor closed 
[TRACE] 2025-06-20 09:27:10.789 - [任务 16(100)][6bcfacf8-ab51-4a73-9b16-14871162ba9a] - Node 6bcfacf8-ab51-4a73-9b16-14871162ba9a[6bcfacf8-ab51-4a73-9b16-14871162ba9a] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.790 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:10.790 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:10.790 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:10.790 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.853 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:10.854 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382830871 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382830871 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:27:10.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 4 ms 
[WARN ] 2025-06-20 09:27:11.103 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:27:11.109 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:27:11.109 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-281ceec0-3979-4e2f-b1dc-549dcc97257a 
[INFO ] 2025-06-20 09:27:11.109 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-281ceec0-3979-4e2f-b1dc-549dcc97257a 
[INFO ] 2025-06-20 09:27:11.110 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:27:11.110 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:27:11.110 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:27:11.110 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] running status set to false 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] schema data cleaned 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] monitor closed 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)][6293c41a-27ae-47bb-821b-05ed6c53aee2] - Node 6293c41a-27ae-47bb-821b-05ed6c53aee2[6293c41a-27ae-47bb-821b-05ed6c53aee2] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:27:11.115 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:28:39.930 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:28:39.936 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382919841 
[TRACE] 2025-06-20 09:28:39.936 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382919841 
[TRACE] 2025-06-20 09:28:39.936 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:28:39.936 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:28:39.936 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:28:40.071 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:28:40.071 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-eb482c3e-fc90-4bc6-b606-ad08774b015a 
[INFO ] 2025-06-20 09:28:40.071 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-eb482c3e-fc90-4bc6-b606-ad08774b015a 
[INFO ] 2025-06-20 09:28:40.071 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.072 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.072 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:28:40.072 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:28:40.079 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] running status set to false 
[TRACE] 2025-06-20 09:28:40.079 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.079 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] monitor closed 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16(100)][00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] - Node 00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94[00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.126 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:28:40.353 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:28:40.368 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382920261 
[TRACE] 2025-06-20 09:28:40.368 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382920261 
[TRACE] 2025-06-20 09:28:40.368 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.368 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:28:40.368 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 14 ms 
[TRACE] 2025-06-20 09:28:40.497 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:28:40.497 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-3d05c24e-0572-422c-b3a8-c5d3ba4488bc 
[INFO ] 2025-06-20 09:28:40.497 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-3d05c24e-0572-422c-b3a8-c5d3ba4488bc 
[INFO ] 2025-06-20 09:28:40.497 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.498 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.498 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:28:40.498 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] running status set to false 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] monitor closed 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)][818fe074-c79f-4550-9538-5b43766184ee] - Node 818fe074-c79f-4550-9538-5b43766184ee[818fe074-c79f-4550-9538-5b43766184ee] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:28:40.504 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:28:40.504 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.565 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.770 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382920700 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382920700 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:28:40.796 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 5 ms 
[WARN ] 2025-06-20 09:28:40.926 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:28:40.932 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:28:40.932 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-5e99816a-fd8a-439c-bd42-46d5c1608b31 
[INFO ] 2025-06-20 09:28:40.932 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-5e99816a-fd8a-439c-bd42-46d5c1608b31 
[INFO ] 2025-06-20 09:28:40.932 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.932 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] running status set to false 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] schema data cleaned 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] monitor closed 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)][5ac93fe7-db39-492e-8d11-2d28283e1e63] - Node 5ac93fe7-db39-492e-8d11-2d28283e1e63[5ac93fe7-db39-492e-8d11-2d28283e1e63] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:28:40.991 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:30:12.336 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.337 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:30:12.577 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:30:12.583 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750383012485 
[TRACE] 2025-06-20 09:30:12.583 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750383012485 
[TRACE] 2025-06-20 09:30:12.583 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:30:12.583 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:30:12.583 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-f671ab54-7e0b-4eca-9191-e2fc746b4be2 
[INFO ] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-f671ab54-7e0b-4eca-9191-e2fc746b4be2 
[INFO ] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:30:12.725 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:30:12.733 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] running status set to false 
[TRACE] 2025-06-20 09:30:12.733 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] schema data cleaned 
[TRACE] 2025-06-20 09:30:12.733 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] monitor closed 
[TRACE] 2025-06-20 09:30:12.733 - [任务 16(100)][fddaf35d-154c-4158-8bca-381410c184a8] - Node fddaf35d-154c-4158-8bca-381410c184a8[fddaf35d-154c-4158-8bca-381410c184a8] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.734 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:30:12.734 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:30:12.734 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:30:12.734 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:30:12.780 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:30:12.780 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:12.780 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:12.780 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.780 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.781 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:12.781 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:30:13.018 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:30:13.018 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750383012917 
[TRACE] 2025-06-20 09:30:13.018 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750383012917 
[TRACE] 2025-06-20 09:30:13.018 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.018 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:30:13.152 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:30:13.152 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-2272a7c0-d695-4bbd-b21d-ab2141692b00 
[INFO ] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-2272a7c0-d695-4bbd-b21d-ab2141692b00 
[INFO ] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:30:13.156 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:30:13.158 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] running status set to false 
[TRACE] 2025-06-20 09:30:13.158 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.158 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] monitor closed 
[TRACE] 2025-06-20 09:30:13.158 - [任务 16(100)][b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] - Node b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab[b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:30:13.159 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:30:13.159 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:30:13.159 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] start preload schema,table counts: 0 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:13.233 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:13.234 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:13.234 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:30:13.478 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:30:13.482 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750383013381 
[TRACE] 2025-06-20 09:30:13.482 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750383013381 
[TRACE] 2025-06-20 09:30:13.482 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.482 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:30:13.625 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 4 ms 
[WARN ] 2025-06-20 09:30:13.625 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:30:13.630 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:30:13.630 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-05c18764-d15d-4a1d-abe2-de5c14eb3a28 
[INFO ] 2025-06-20 09:30:13.630 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-05c18764-d15d-4a1d-abe2-de5c14eb3a28 
[INFO ] 2025-06-20 09:30:13.630 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.631 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.632 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:30:13.632 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] running status set to false 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] schema data cleaned 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] monitor closed 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)][3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] - Node 3f374fc8-5f0c-4b64-8726-ead5c2d34fd1[3f374fc8-5f0c-4b64-8726-ead5c2d34fd1] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 09:30:13.697 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.178 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:03:31.444 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:03:31.451 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750388611322 
[TRACE] 2025-06-20 11:03:31.451 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750388611322 
[TRACE] 2025-06-20 11:03:31.451 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:03:31.451 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:03:31.451 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:03:31.639 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:03:31.639 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-3d4264f8-27de-417f-bd8a-d302c32ccd84 
[INFO ] 2025-06-20 11:03:31.639 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-3d4264f8-27de-417f-bd8a-d302c32ccd84 
[INFO ] 2025-06-20 11:03:31.639 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:03:31.640 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:03:31.640 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:03:31.640 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] running status set to false 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] schema data cleaned 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] monitor closed 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)][0899a15c-0d45-4d08-9eb8-407aeb48697c] - Node 0899a15c-0d45-4d08-9eb8-407aeb48697c[0899a15c-0d45-4d08-9eb8-407aeb48697c] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.702 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:31.908 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750388611855 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750388611855 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:03:31.960 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:03:32.101 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:03:32.102 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-13b6379e-1b87-4eb2-8e1d-d309acc88a7d 
[INFO ] 2025-06-20 11:03:32.102 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-13b6379e-1b87-4eb2-8e1d-d309acc88a7d 
[INFO ] 2025-06-20 11:03:32.102 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.102 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.103 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:03:32.103 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] running status set to false 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] monitor closed 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)][31abf314-0cd1-4533-b5af-10eaad1ea27b] - Node 31abf314-0cd1-4533-b5af-10eaad1ea27b[31abf314-0cd1-4533-b5af-10eaad1ea27b] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:03:32.180 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:03:32.180 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:03:32.180 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:32.180 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:32.180 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:32.181 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:32.181 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:32.181 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750388612331 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750388612331 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:03:32.436 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[WARN ] 2025-06-20 11:03:32.594 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:03:32.596 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:03:32.596 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-f2a4d61c-358a-4ff4-8130-cd51dc02d0fc 
[INFO ] 2025-06-20 11:03:32.596 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-f2a4d61c-358a-4ff4-8130-cd51dc02d0fc 
[INFO ] 2025-06-20 11:03:32.597 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:03:32.597 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] running status set to false 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] schema data cleaned 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] monitor closed 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)][d31efdf9-6d77-4df1-9eaa-b0ceca479834] - Node d31efdf9-6d77-4df1-9eaa-b0ceca479834[d31efdf9-6d77-4df1-9eaa-b0ceca479834] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:05.769 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:14:06.038 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:14:06.041 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389245928 
[TRACE] 2025-06-20 11:14:06.042 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389245928 
[TRACE] 2025-06-20 11:14:06.042 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.042 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:14:06.042 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:14:06.215 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:14:06.216 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b9b9567d-517e-4d07-ac8d-19fdffa30d0c 
[INFO ] 2025-06-20 11:14:06.217 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b9b9567d-517e-4d07-ac8d-19fdffa30d0c 
[INFO ] 2025-06-20 11:14:06.217 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.217 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.217 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:14:06.217 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] running status set to false 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] monitor closed 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)][d996d311-3610-4772-864c-2b2f7b2a44e1] - Node d996d311-3610-4772-864c-2b2f7b2a44e1[d996d311-3610-4772-864c-2b2f7b2a44e1] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.272 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:06.273 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:14:06.273 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389246416 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389246416 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:14:06.530 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:14:06.721 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:14:06.723 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b41fc0fb-d9d2-466b-a52c-c279fc1e6a78 
[INFO ] 2025-06-20 11:14:06.723 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b41fc0fb-d9d2-466b-a52c-c279fc1e6a78 
[INFO ] 2025-06-20 11:14:06.723 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.724 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.724 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:14:06.724 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:14:06.731 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] running status set to false 
[TRACE] 2025-06-20 11:14:06.731 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] schema data cleaned 
[TRACE] 2025-06-20 11:14:06.731 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] monitor closed 
[TRACE] 2025-06-20 11:14:06.731 - [任务 16(100)][00a74341-94c4-47ed-a4a8-6bcd7d6bd008] - Node 00a74341-94c4-47ed-a4a8-6bcd7d6bd008[00a74341-94c4-47ed-a4a8-6bcd7d6bd008] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.732 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:06.732 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:06.732 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:06.732 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:06.802 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:14:07.048 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:14:07.051 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389246949 
[TRACE] 2025-06-20 11:14:07.051 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389246949 
[TRACE] 2025-06-20 11:14:07.051 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:14:07.051 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:14:07.051 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 3 ms 
[WARN ] 2025-06-20 11:14:07.191 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:14:07.191 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:14:07.198 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a92600e1-e2de-43ab-b356-1186baa3ccec 
[INFO ] 2025-06-20 11:14:07.198 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a92600e1-e2de-43ab-b356-1186baa3ccec 
[INFO ] 2025-06-20 11:14:07.199 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:07.199 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:14:07.199 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:14:07.199 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 8 ms 
[TRACE] 2025-06-20 11:14:07.200 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] running status set to false 
[TRACE] 2025-06-20 11:14:07.200 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] schema data cleaned 
[TRACE] 2025-06-20 11:14:07.200 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] monitor closed 
[TRACE] 2025-06-20 11:14:07.200 - [任务 16(100)][f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] - Node f3d429b8-5240-464b-b5a7-f9da1c8d6f6e[f3d429b8-5240-464b-b5a7-f9da1c8d6f6e] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.201 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:07.201 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:07.201 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:07.201 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:36.510 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.511 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:14:36.740 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:14:36.743 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389276648 
[TRACE] 2025-06-20 11:14:36.743 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389276648 
[TRACE] 2025-06-20 11:14:36.743 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:14:36.743 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:14:36.743 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:14:36.894 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:14:36.894 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-c0855f0a-6929-422a-b02c-b433382f5d91 
[INFO ] 2025-06-20 11:14:36.894 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-c0855f0a-6929-422a-b02c-b433382f5d91 
[INFO ] 2025-06-20 11:14:36.894 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:36.895 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:14:36.895 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:14:36.895 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:14:36.903 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] running status set to false 
[TRACE] 2025-06-20 11:14:36.903 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] schema data cleaned 
[TRACE] 2025-06-20 11:14:36.903 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] monitor closed 
[TRACE] 2025-06-20 11:14:36.904 - [任务 16(100)][5e23d608-a8a7-4d23-bdb6-a3099c8522df] - Node 5e23d608-a8a7-4d23-bdb6-a3099c8522df[5e23d608-a8a7-4d23-bdb6-a3099c8522df] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.904 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:36.904 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:36.904 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:36.965 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:14:37.217 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:14:37.220 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389277109 
[TRACE] 2025-06-20 11:14:37.220 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389277109 
[TRACE] 2025-06-20 11:14:37.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:14:37.221 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 4 ms 
[TRACE] 2025-06-20 11:14:37.360 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:14:37.361 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-72a9b131-3418-4733-a475-5d5eb4e90620 
[INFO ] 2025-06-20 11:14:37.361 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-72a9b131-3418-4733-a475-5d5eb4e90620 
[INFO ] 2025-06-20 11:14:37.361 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.361 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.361 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:14:37.371 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 1 ms 
[TRACE] 2025-06-20 11:14:37.371 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] running status set to false 
[TRACE] 2025-06-20 11:14:37.371 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.371 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] monitor closed 
[TRACE] 2025-06-20 11:14:37.371 - [任务 16(100)][371daf30-5ebb-47f0-9717-07a65a89a81a] - Node 371daf30-5ebb-47f0-9717-07a65a89a81a[371daf30-5ebb-47f0-9717-07a65a89a81a] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:37.372 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:37.372 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:37.372 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:37.441 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:37.646 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:14:37.694 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:14:37.694 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389277603 
[TRACE] 2025-06-20 11:14:37.694 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389277603 
[TRACE] 2025-06-20 11:14:37.694 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.694 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:14:37.695 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[WARN ] 2025-06-20 11:14:37.860 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:14:37.860 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:14:37.863 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b4d5bb86-1cfa-4167-8f93-9345f79691e3 
[INFO ] 2025-06-20 11:14:37.864 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b4d5bb86-1cfa-4167-8f93-9345f79691e3 
[INFO ] 2025-06-20 11:14:37.864 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.864 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.864 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:14:37.870 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 4 ms 
[TRACE] 2025-06-20 11:14:37.870 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] running status set to false 
[TRACE] 2025-06-20 11:14:37.870 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] schema data cleaned 
[TRACE] 2025-06-20 11:14:37.870 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] monitor closed 
[TRACE] 2025-06-20 11:14:37.870 - [任务 16(100)][b77d54b4-b692-42cf-8040-b398b7ec0788] - Node b77d54b4-b692-42cf-8040-b398b7ec0788[b77d54b4-b692-42cf-8040-b398b7ec0788] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:14:37.871 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:14:37.871 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:14:37.871 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:14:37.932 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:44:10.856 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:44:10.857 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:44:10.857 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:44:10.859 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:44:10.860 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:44:10.860 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:44:10.861 - [任务 16(100)][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:45:41.334 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:45:41.335 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750391050914 
[TRACE] 2025-06-20 11:45:41.335 - [任务 16(100)][TWPOS_PS_INVC] - PDK connector node released: HazelcastSampleSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750391050914 
[TRACE] 2025-06-20 11:45:41.335 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:45:41.336 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:45:41.539 - [任务 16(100)][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 8 ms 
[TRACE] 2025-06-20 11:46:20.308 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:46:20.309 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e57d461b-5998-42e7-9403-3fab5f561d5a 
[INFO ] 2025-06-20 11:46:20.314 - [任务 16(100)][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e57d461b-5998-42e7-9403-3fab5f561d5a 
[INFO ] 2025-06-20 11:46:20.314 - [任务 16(100)][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.318 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.319 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:46:20.334 - [任务 16(100)][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 13 ms 
[TRACE] 2025-06-20 11:46:20.334 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] running status set to false 
[TRACE] 2025-06-20 11:46:20.334 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.334 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] monitor closed 
[TRACE] 2025-06-20 11:46:20.335 - [任务 16(100)][3eb4e912-57d8-490d-a8f2-dc34c411768b] - Node 3eb4e912-57d8-490d-a8f2-dc34c411768b[3eb4e912-57d8-490d-a8f2-dc34c411768b] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.336 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:46:20.336 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:46:20.336 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:46:20.336 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.416 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.417 - [任务 16(100)][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:46:20.580 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:46:20.580 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750391180450 
[TRACE] 2025-06-20 11:46:20.580 - [任务 16(100)][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSampleSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750391180450 
[TRACE] 2025-06-20 11:46:20.580 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.580 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:46:20.581 - [任务 16(100)][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 11:46:20.736 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:46:20.736 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-d7e31b52-5b2b-4fa2-b8af-454de3944021 
[INFO ] 2025-06-20 11:46:20.736 - [任务 16(100)][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-d7e31b52-5b2b-4fa2-b8af-454de3944021 
[INFO ] 2025-06-20 11:46:20.736 - [任务 16(100)][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.737 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.737 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:46:20.737 - [任务 16(100)][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 5 ms 
[TRACE] 2025-06-20 11:46:20.742 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] running status set to false 
[TRACE] 2025-06-20 11:46:20.742 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.742 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] monitor closed 
[TRACE] 2025-06-20 11:46:20.742 - [任务 16(100)][7ceebc07-361c-45fa-b129-a7245a5c5ad6] - Node 7ceebc07-361c-45fa-b129-a7245a5c5ad6[7ceebc07-361c-45fa-b129-a7245a5c5ad6] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.743 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:46:20.743 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:46:20.743 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:46:20.830 - [任务 16(100)] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:46:20.830 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:20.830 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:20.830 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] start preload schema,table counts: 0 
[TRACE] 2025-06-20 11:46:20.831 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.831 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.831 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:20.955 - [任务 16(100)][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:46:20.955 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:46:20.960 - [任务 16(100)][MDM_TWPOS] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750391180855 
[TRACE] 2025-06-20 11:46:20.960 - [任务 16(100)][MDM_TWPOS] - PDK connector node released: HazelcastSampleSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750391180855 
[TRACE] 2025-06-20 11:46:20.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:46:20.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:46:20.960 - [任务 16(100)][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 5 ms 
[WARN ] 2025-06-20 11:46:21.105 - [任务 16(100)][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:46:21.105 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:46:21.107 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-8b1128c8-d2b7-4e57-90c6-83d9836139a1 
[INFO ] 2025-06-20 11:46:21.108 - [任务 16(100)][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-8b1128c8-d2b7-4e57-90c6-83d9836139a1 
[INFO ] 2025-06-20 11:46:21.108 - [任务 16(100)][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d96-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[TRACE] 2025-06-20 11:46:21.108 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:46:21.109 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:46:21.109 - [任务 16(100)][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:46:21.113 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] running status set to false 
[TRACE] 2025-06-20 11:46:21.113 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] schema data cleaned 
[TRACE] 2025-06-20 11:46:21.113 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] monitor closed 
[TRACE] 2025-06-20 11:46:21.114 - [任务 16(100)][8b6b49c5-e547-4d18-8783-a3fd56f6ea13] - Node 8b6b49c5-e547-4d18-8783-a3fd56f6ea13[8b6b49c5-e547-4d18-8783-a3fd56f6ea13] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.114 - [任务 16(100)] - Closed task monitor(s)
null 
[TRACE] 2025-06-20 11:46:21.114 - [任务 16(100)] - Closed TaskInspect instance
  null 
[TRACE] 2025-06-20 11:46:21.114 - [任务 16(100)] - Closed task auto recovery instance
  null 
[TRACE] 2025-06-20 11:46:21.191 - [任务 16(100)] - Stopped task aspect(s) 
