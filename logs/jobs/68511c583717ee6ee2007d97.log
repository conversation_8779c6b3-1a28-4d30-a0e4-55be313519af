[TRACE] 2025-06-20 09:10:34.868 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-1031dead-08db-418e-962b-853022a8ca64 complete, cost 558ms 
[TRACE] 2025-06-20 09:10:35.416 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-7e5268f4-5d58-4fe6-a43e-6d01270dc543 complete, cost 530ms 
[TRACE] 2025-06-20 09:10:35.958 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b4cfd0f7-7909-4287-a035-7178f307f376 complete, cost 493ms 
[TRACE] 2025-06-20 09:10:40.327 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-0618928d-b408-49d6-b5f1-ae820b934189 complete, cost 326ms 
[TRACE] 2025-06-20 09:10:40.634 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d6c17535-3bd7-44ce-b857-97d27897c9ca complete, cost 292ms 
[TRACE] 2025-06-20 09:10:40.987 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-03679687-8422-403a-9f07-934ff32ca99d complete, cost 325ms 
[TRACE] 2025-06-20 09:10:41.736 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-9f117654-d4bd-4263-9781-b2c47be4a6d3 complete, cost 284ms 
[TRACE] 2025-06-20 09:10:42.044 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-fb9e3afe-34b4-412f-b13e-0747f1c9fbaf complete, cost 294ms 
[TRACE] 2025-06-20 09:10:42.378 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-546148dd-60b2-433b-a416-2d44fa555807 complete, cost 296ms 
[TRACE] 2025-06-20 09:10:44.715 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-ba28802f-8b9e-4dcc-be13-aab8372d3d2d complete, cost 299ms 
[TRACE] 2025-06-20 09:10:44.952 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-97cf41bb-6a50-4045-a60c-e552c11317f3 complete, cost 361ms 
[TRACE] 2025-06-20 09:10:45.040 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3d3ea2e9-a66e-435c-92ed-efa86dd7d111 complete, cost 312ms 
[TRACE] 2025-06-20 09:10:45.302 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-61ed5719-8616-468e-9ca9-16f590969123 complete, cost 90609ms 
[TRACE] 2025-06-20 09:10:45.383 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-829c8b9e-6679-4f9f-8ddf-8fd8fd9fe02c complete, cost 418ms 
[TRACE] 2025-06-20 09:10:45.390 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-167ff8ee-6220-49f6-ba22-70a28c9f753a complete, cost 325ms 
[TRACE] 2025-06-20 09:10:45.632 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b6f02481-98e2-4a7a-a35b-327ee0fec1f4 complete, cost 318ms 
[TRACE] 2025-06-20 09:10:45.712 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f016637c-da99-40a3-bc3d-e366f64a394b complete, cost 300ms 
[TRACE] 2025-06-20 09:10:46.013 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-6ff4498b-4922-4bc6-b862-4cea04caec4c complete, cost 354ms 
[TRACE] 2025-06-20 09:10:48.062 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-6e7f17c7-a6b6-4ea3-9539-40f1b2b9a6ff complete, cost 279ms 
[TRACE] 2025-06-20 09:10:48.297 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-c3552f2c-350b-44ce-aeb5-6aeb7061a673 complete, cost 326ms 
[TRACE] 2025-06-20 09:10:48.385 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-4f8d650b-63c4-497d-866d-7eefb48cdd95 complete, cost 310ms 
[TRACE] 2025-06-20 09:10:48.652 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5cb1c5a2-22b4-43e2-92e2-6b723312af88 complete, cost 343ms 
[TRACE] 2025-06-20 09:10:48.672 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5542c0ad-2917-46db-b649-98bd41316e35 complete, cost 90308ms 
[TRACE] 2025-06-20 09:10:48.738 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-750112f0-7bf7-4eda-8a18-235fbdff37a6 complete, cost 328ms 
[TRACE] 2025-06-20 09:10:48.959 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-c167cef8-0b02-4875-9a9c-ba717de924e5 complete, cost 275ms 
[TRACE] 2025-06-20 09:10:49.043 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f4e2304d-f29e-4eec-a015-1d1c4974b614 complete, cost 365ms 
[TRACE] 2025-06-20 09:10:49.280 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-8d1f1faf-29ba-47cc-959a-ad28bc33e1cd complete, cost 296ms 
[TRACE] 2025-06-20 09:10:50.201 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-81641864-20ba-4b1f-860a-8286d26a17e7 complete, cost 289ms 
[TRACE] 2025-06-20 09:10:50.486 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-4b24cad5-5f95-4661-8b7e-08256308e5a3 complete, cost 272ms 
[TRACE] 2025-06-20 09:10:50.815 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-effca3ea-5688-49fc-ba83-5281d9cfc766 complete, cost 303ms 
[TRACE] 2025-06-20 09:10:52.013 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-052b45ab-e104-4405-a353-17a475addab8 complete, cost 90304ms 
[TRACE] 2025-06-20 09:10:52.322 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-52d678cb-8146-49c0-9d2c-820df85cc99b complete, cost 297ms 
[TRACE] 2025-06-20 09:10:52.603 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-078efe4b-6060-472f-8b1e-e44ddc17a111 complete, cost 256ms 
[TRACE] 2025-06-20 09:10:52.730 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-c0e8c6ad-ab8a-4abc-b344-94bbb299cf5b complete, cost 90265ms 
[TRACE] 2025-06-20 09:10:53.027 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-02f73a3a-6c78-4134-9f70-083b345a1a1e complete, cost 286ms 
[TRACE] 2025-06-20 09:10:53.339 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-c9905877-a6e2-4340-a003-2e85e4798a37 complete, cost 287ms 
[TRACE] 2025-06-20 09:10:56.445 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-be1cf111-c9fd-4731-b45d-3ba72b4711cd complete, cost 90274ms 
[TRACE] 2025-06-20 09:10:56.736 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f61e46d6-fcce-47be-a7fb-ae0175c6e83a complete, cost 280ms 
[TRACE] 2025-06-20 09:10:57.004 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-835add9e-bd79-4bee-a57e-29acbaeac4a4 complete, cost 90277ms 
[TRACE] 2025-06-20 09:10:57.091 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-a16759ce-0bdc-4068-b30f-c4e3c50011df complete, cost 331ms 
[TRACE] 2025-06-20 09:10:57.324 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-1d1876fd-c851-460f-8934-e702d1feed98 complete, cost 308ms 
[TRACE] 2025-06-20 09:10:57.635 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-775d43be-0c2e-49fc-aae3-61f35d660ace complete, cost 287ms 
[TRACE] 2025-06-20 09:10:58.250 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-eff5bfb8-632b-47c4-b9ae-2d8ba562cc9d complete, cost 90286ms 
[TRACE] 2025-06-20 09:10:58.527 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-97b7172d-3468-4ec3-975c-9ab9e37c2cce complete, cost 265ms 
[TRACE] 2025-06-20 09:10:58.842 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b29141a0-bcf3-49f9-8a3c-70ad1881d0d4 complete, cost 290ms 
[TRACE] 2025-06-20 09:11:15.210 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-dc7aacb9-4b25-416a-be6c-43f766d641b1 complete, cost 302ms 
[TRACE] 2025-06-20 09:11:15.510 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-76e8a5f3-d4a8-46f1-920b-dc972481c968 complete, cost 288ms 
[TRACE] 2025-06-20 09:11:15.828 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5f5f939f-ea86-45b6-be85-5662b008017d complete, cost 293ms 
[TRACE] 2025-06-20 09:11:17.104 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 09:11:17.222 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 09:11:17.482 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f045c2eb-c101-43cb-bae1-5d7bea5834ba complete, cost 293ms 
[TRACE] 2025-06-20 09:11:17.784 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-2ec6d79c-8ad5-476a-8aec-25e6d2b3a129 complete, cost 290ms 
[TRACE] 2025-06-20 09:11:18.100 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5e9baf57-d99e-4fd6-8940-210f3cae6712 complete, cost 292ms 
[INFO ] 2025-06-20 09:11:18.184 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 09:11:18.185 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 09:11:18.714 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 09:11:18.714 - [任务 16] - Task started 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.749 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 09:11:18.750 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:11:18.750 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 09:11:18.751 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 09:11:18.751 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.751 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.751 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.752 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.753 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:11:18.754 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.754 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:11:18.754 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.754 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:11:18.754 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[INFO ] 2025-06-20 09:11:18.978 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:11:18.978 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 09:11:18.978 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 09:11:18.978 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:11:19.008 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750381869,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-20 09:11:19.049 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 09:11:19.052 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 09:11:19.052 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 09:11:19.052 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 09:11:19.052 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:11:19.099 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750381869,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:11:19.099 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:11:19.106 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 09:11:19.106 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 09:11:19.113 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 09:11:19.113 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 09:11:19.129 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 09:11:19.129 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 09:11:19.129 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 09:11:19.129 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 09:11:19.129 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 09:11:19.130 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750381869,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:11:19.130 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 09:11:19.137 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 09:11:19.137 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[INFO ] 2025-06-20 09:11:19.137 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:11:19.137 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 09:11:19.137 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-06-20 09:11:19.137 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:11:19.137 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[TRACE] 2025-06-20 09:11:19.165 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[INFO ] 2025-06-20 09:11:19.165 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750381879,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:11:19.185 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:11:19.185 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[TRACE] 2025-06-20 09:11:19.242 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:11:19.242 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 09:11:19.242 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-20 09:11:19.258 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:11:19.258 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[TRACE] 2025-06-20 09:11:21.283 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@11d54784: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 09:11:21.647 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@11d54784: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 09:11:22.186 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 09:11:22.186 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:11:22.187 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 09:11:22.187 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 09:11:22.187 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[TRACE] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[INFO ] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750381879,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:11:22.195 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:11:22.213 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 09:11:22.213 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:11:22.213 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:11:24.266 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 09:11:24.266 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:11:24.267 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 09:11:24.267 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 09:11:24.267 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 09:11:24.269 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 09:11:24.275 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 09:11:24.275 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 09:11:24.275 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 09:11:24.275 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 09:11:24.275 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 09:11:24.276 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750381869,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:11:24.276 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:11:24.279 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 09:11:24.283 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:11:24.283 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:11:26.344 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:11:26.345 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:22:39.208 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:22:39.216 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381878884 
[TRACE] 2025-06-20 09:22:39.219 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750381878884 
[TRACE] 2025-06-20 09:22:39.219 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.219 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:22:39.219 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 10 ms 
[TRACE] 2025-06-20 09:22:39.219 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:22:39.220 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b29516cf-3af9-4fa3-86f7-7ed8e7cbb6e1 
[INFO ] 2025-06-20 09:22:39.223 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b29516cf-3af9-4fa3-86f7-7ed8e7cbb6e1 
[INFO ] 2025-06-20 09:22:39.224 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:22:39.224 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-db66cf81-df24-4dcd-b7a3-4b9df6010c9e 
[INFO ] 2025-06-20 09:22:39.224 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-db66cf81-df24-4dcd-b7a3-4b9df6010c9e 
[INFO ] 2025-06-20 09:22:39.224 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.224 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.224 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:22:39.225 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:22:39.227 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381878923 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750381878923 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:39.228 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:22:39.229 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-2446edd2-ed14-4511-bfda-42fdf003620c 
[INFO ] 2025-06-20 09:22:39.233 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-2446edd2-ed14-4511-bfda-42fdf003620c 
[INFO ] 2025-06-20 09:22:39.233 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:22:39.233 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-a6bb2d72-5e1a-4860-80e5-7901849cbe1a 
[INFO ] 2025-06-20 09:22:39.233 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-a6bb2d72-5e1a-4860-80e5-7901849cbe1a 
[INFO ] 2025-06-20 09:22:39.233 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.233 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.233 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:22:39.233 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:22:39.233 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:22:39.235 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381878879 
[TRACE] 2025-06-20 09:22:39.239 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750381878879 
[TRACE] 2025-06-20 09:22:39.239 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.239 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:22:39.239 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:22:39.239 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:22:39.239 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-23dccd6c-6a94-429c-8e0e-26160066e084 
[INFO ] 2025-06-20 09:22:39.243 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-23dccd6c-6a94-429c-8e0e-26160066e084 
[INFO ] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-ce130301-2527-415f-872c-257e100fe66e 
[INFO ] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-ce130301-2527-415f-872c-257e100fe66e 
[INFO ] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 09:22:39.244 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 09:22:39.247 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.249 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 09:22:39.249 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:22:39.249 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 09:22:39.256 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750381879020 
[TRACE] 2025-06-20 09:22:39.256 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750381879020 
[TRACE] 2025-06-20 09:22:39.256 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 09:22:39.256 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 09:22:39.459 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 9 ms 
[TRACE] 2025-06-20 09:22:39.517 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 09:22:40.132 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 09:22:47.838 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 09:22:48.842 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@205e2669 
[TRACE] 2025-06-20 09:22:48.844 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@364641ac 
[TRACE] 2025-06-20 09:22:48.845 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 09:22:48.968 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:22:48.968 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 09:22:48.968 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 09:22:49.021 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:22:49.021 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:22:57.397 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-cd331c43-862f-4863-beed-6ba2dac15c57 complete, cost 543ms 
[TRACE] 2025-06-20 09:22:57.463 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-9d5ccb6b-111b-491e-b795-c4a281ed3150 complete, cost 521ms 
[TRACE] 2025-06-20 09:22:57.916 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3dc0d96c-af08-4138-9bcc-b6dca0d70551 complete, cost 439ms 
[TRACE] 2025-06-20 09:22:57.984 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-0d9427b8-5646-4ff6-990f-2ba3585509df complete, cost 562ms 
[TRACE] 2025-06-20 09:22:58.436 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-e334b66d-132b-4c2d-9888-8c62a06358c9 complete, cost 482ms 
[TRACE] 2025-06-20 09:22:58.509 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-002a5872-e8b5-4196-b2b9-15a0808a28bc complete, cost 495ms 
[TRACE] 2025-06-20 09:22:59.064 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-7d64c490-b4b8-468a-9416-2582b899acae complete, cost 303ms 
[TRACE] 2025-06-20 09:22:59.383 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-2484d160-afdc-4024-af13-8a8f5ed10ee1 complete, cost 308ms 
[TRACE] 2025-06-20 09:22:59.454 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-e7a9caf8-5a63-463e-bd20-ce3772ec9bb4 complete, cost 344ms 
[TRACE] 2025-06-20 09:22:59.741 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f7a6f251-17b5-48d4-85c3-69f0f972abe6 complete, cost 332ms 
[TRACE] 2025-06-20 09:22:59.811 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d91d4e7e-173c-41a8-a150-d1a29991167b complete, cost 343ms 
[TRACE] 2025-06-20 09:23:00.130 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-de36aa1b-0280-431a-a5b8-9bb04368807c complete, cost 524ms 
[TRACE] 2025-06-20 09:23:00.195 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d1a87780-fb64-40ec-98ba-349bf1c61705 complete, cost 359ms 
[TRACE] 2025-06-20 09:23:00.422 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-ba445fde-1b3c-4457-86e7-289caead96da complete, cost 280ms 
[TRACE] 2025-06-20 09:23:00.745 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-2f35fb91-a410-4606-949f-7cd8e69496cb complete, cost 296ms 
[TRACE] 2025-06-20 09:23:10.060 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3f38fd9d-fd1c-4aae-a6cc-a52b37ee1a56 complete, cost 289ms 
[TRACE] 2025-06-20 09:23:10.370 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-0bb7dead-d4b2-4b45-b58b-498323a269ff complete, cost 299ms 
[TRACE] 2025-06-20 09:23:10.724 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-079d072f-a931-49ba-98e8-790dc323937a complete, cost 304ms 
[TRACE] 2025-06-20 09:23:12.045 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3ab21b55-8a83-43fd-a2c6-076f784b8a0e complete, cost 295ms 
[TRACE] 2025-06-20 09:23:12.354 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-c1618775-2b45-4354-bf08-db00bb601b74 complete, cost 296ms 
[TRACE] 2025-06-20 09:23:12.671 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-1a66752e-beb3-46b0-8b12-077313f11395 complete, cost 290ms 
[TRACE] 2025-06-20 09:23:15.113 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 09:23:15.116 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 09:23:15.483 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5da2471e-58bc-4a34-a517-f6927562c86a complete, cost 291ms 
[TRACE] 2025-06-20 09:23:15.792 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-933aa508-d092-4bd2-baba-5ad1183ceb62 complete, cost 295ms 
[TRACE] 2025-06-20 09:23:16.137 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d46605bc-26bf-468a-94fa-a910ab5d0e05 complete, cost 321ms 
[INFO ] 2025-06-20 09:23:16.193 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 09:23:16.398 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 09:23:16.697 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 09:23:16.755 - [任务 16] - Task started 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.755 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.756 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 09:23:16.756 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 09:23:16.756 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 09:23:16.757 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.757 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.757 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 1 ms 
[TRACE] 2025-06-20 09:23:16.757 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.757 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:23:16.760 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.761 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.761 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:23:16.763 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:23:16.763 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:23:16.965 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[INFO ] 2025-06-20 09:23:16.966 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 09:23:16.966 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 09:23:16.966 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 09:23:16.966 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:23:17.034 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:23:17.034 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:23:17.034 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 09:23:17.034 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 09:23:17.034 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:23:17.044 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 09:23:17.044 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 09:23:17.044 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 09:23:17.044 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:23:17.062 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:23:17.086 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-20 09:23:17.086 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 09:23:17.127 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:23:17.128 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[INFO ] 2025-06-20 09:23:17.146 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[INFO ] 2025-06-20 09:23:17.146 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:23:17.146 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[TRACE] 2025-06-20 09:23:17.150 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 09:23:17.150 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 09:23:17.150 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 09:23:17.159 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 09:23:17.160 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:23:17.160 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:23:17.174 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[INFO ] 2025-06-20 09:23:17.174 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:23:17.174 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 09:23:17.174 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[TRACE] 2025-06-20 09:23:17.271 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:23:17.271 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 09:23:17.272 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[TRACE] 2025-06-20 09:23:19.189 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7bccfdd1: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 09:23:19.189 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7bccfdd1: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 09:23:19.717 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 09:23:19.717 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:23:19.718 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 09:23:19.718 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 09:23:19.718 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[TRACE] 2025-06-20 09:23:19.721 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[INFO ] 2025-06-20 09:23:19.727 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 09:23:19.727 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 09:23:19.727 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 09:23:19.727 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 09:23:19.728 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 09:23:19.728 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:23:19.728 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:23:19.749 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 09:23:19.749 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:23:19.954 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:23:22.295 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 09:23:22.295 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:23:22.295 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 09:23:22.296 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 09:23:22.296 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 09:23:22.301 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 09:23:22.301 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 09:23:22.301 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 09:23:22.301 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 09:23:22.302 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 09:23:22.302 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 09:23:22.302 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:23:22.302 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:23:22.308 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:23:22.308 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:23:22.510 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 09:23:24.360 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:23:24.360 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382596947 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382596947 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:25:40.325 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:25:40.327 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-99c18446-c274-4312-913e-bbc6cfe46d15 
[INFO ] 2025-06-20 09:25:40.327 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-99c18446-c274-4312-913e-bbc6cfe46d15 
[INFO ] 2025-06-20 09:25:40.328 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:25:40.329 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-0273e499-2adc-4ac2-85b8-12385ffcd6d0 
[INFO ] 2025-06-20 09:25:40.329 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-0273e499-2adc-4ac2-85b8-12385ffcd6d0 
[INFO ] 2025-06-20 09:25:40.329 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.332 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.333 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:25:40.333 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:25:40.333 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382596864 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382596864 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 2 ms 
[TRACE] 2025-06-20 09:25:40.336 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:25:40.337 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-01727b98-07b1-44f1-8764-227feb935e23 
[INFO ] 2025-06-20 09:25:40.337 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-01727b98-07b1-44f1-8764-227feb935e23 
[INFO ] 2025-06-20 09:25:40.337 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:25:40.338 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-6e650934-e2a8-4402-a4ad-2e5b8564c5b0 
[INFO ] 2025-06-20 09:25:40.338 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-6e650934-e2a8-4402-a4ad-2e5b8564c5b0 
[INFO ] 2025-06-20 09:25:40.338 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.339 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.339 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:25:40.339 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:25:40.340 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:25:40.342 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382596895 
[TRACE] 2025-06-20 09:25:40.343 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382596895 
[TRACE] 2025-06-20 09:25:40.343 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.343 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:25:40.343 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:25:40.343 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:25:40.346 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-2568bf53-51af-408d-a126-e033e0941c43 
[INFO ] 2025-06-20 09:25:40.346 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-2568bf53-51af-408d-a126-e033e0941c43 
[INFO ] 2025-06-20 09:25:40.346 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:25:40.348 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-901cd2e7-da37-419e-9be0-41931f2cc090 
[INFO ] 2025-06-20 09:25:40.348 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-901cd2e7-da37-419e-9be0-41931f2cc090 
[INFO ] 2025-06-20 09:25:40.348 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.349 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.349 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:25:40.349 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:25:40.349 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 09:25:40.349 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 09:25:40.351 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 09:25:40.360 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382597079 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382597079 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 09:25:40.361 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 09:25:40.439 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:25:40.439 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 09:25:40.642 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 09:25:41.014 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 09:25:49.147 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 09:25:50.152 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@6e54bb1 
[TRACE] 2025-06-20 09:25:50.153 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2000fe91 
[TRACE] 2025-06-20 09:25:50.153 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 09:25:50.269 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:25:50.269 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 09:25:50.269 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 09:25:50.312 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:25:50.312 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:26:12.709 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-bbb934c3-d982-41b4-af7c-aeb1b38d5b2d complete, cost 293ms 
[TRACE] 2025-06-20 09:26:13.059 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-55652850-ea3d-4694-9679-380444315742 complete, cost 336ms 
[TRACE] 2025-06-20 09:26:13.405 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-02c5e366-cf4f-4d44-bbd1-9e2fac1e28a2 complete, cost 308ms 
[TRACE] 2025-06-20 09:26:43.971 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5ffc7a73-b0bb-4cbb-8703-33be5bb0d3d3 complete, cost 292ms 
[TRACE] 2025-06-20 09:26:44.263 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-08ce2bc0-7b7a-4d14-a056-127cc0098b38 complete, cost 279ms 
[TRACE] 2025-06-20 09:26:44.588 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-1e6363a4-89ce-4fde-b383-3db2fb094970 complete, cost 298ms 
[TRACE] 2025-06-20 09:27:04.462 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f3bc618d-626c-4c3c-aca3-0bacdf45d3b0 complete, cost 293ms 
[TRACE] 2025-06-20 09:27:04.766 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b501d238-53c9-4adf-a4c6-335672f6b729 complete, cost 292ms 
[TRACE] 2025-06-20 09:27:05.074 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b1b44447-d62f-438f-9a8e-a1d3e09c7845 complete, cost 283ms 
[TRACE] 2025-06-20 09:27:07.049 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-945791c5-41e3-48e9-8ddb-7009f72ffb2c complete, cost 309ms 
[TRACE] 2025-06-20 09:27:07.340 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-6a1cbf58-3ef7-4b38-a3bc-cccb3f1fa9e4 complete, cost 278ms 
[TRACE] 2025-06-20 09:27:07.647 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3901342c-34b1-4756-bba8-1c3e41799028 complete, cost 283ms 
[TRACE] 2025-06-20 09:27:10.116 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 09:27:10.117 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 09:27:10.466 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-7aef4af3-fa87-454d-a3cd-39226ff2f2b6 complete, cost 269ms 
[TRACE] 2025-06-20 09:27:10.790 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-6bcfacf8-ab51-4a73-9b16-14871162ba9a complete, cost 312ms 
[TRACE] 2025-06-20 09:27:11.114 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-6293c41a-27ae-47bb-821b-05ed6c53aee2 complete, cost 300ms 
[INFO ] 2025-06-20 09:27:11.177 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 09:27:11.255 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 09:27:11.256 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 09:27:11.326 - [任务 16] - Task started 
[TRACE] 2025-06-20 09:27:11.326 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.327 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.328 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 09:27:11.328 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 09:27:11.328 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 09:27:11.329 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.330 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.330 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:27:11.330 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:27:11.330 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:27:11.331 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:27:11.331 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:27:11.361 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:27:11.361 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 09:27:11.361 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-20 09:27:11.440 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 09:27:11.440 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 09:27:11.441 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[INFO ] 2025-06-20 09:27:11.443 - [任务 16][TWPOS_PS_INVC_TXN] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 09:27:11.443 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing batch read offset: {"TWPOS_PS_INVC_TXN":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:27:11.500 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 09:27:11.500 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 09:27:11.500 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[INFO ] 2025-06-20 09:27:11.500 - [任务 16][TWPOS_PS_INVC] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC] - Use existing batch read offset: {"TWPOS_PS_INVC":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:27:11.527 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:27:11.568 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[INFO ] 2025-06-20 09:27:11.568 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:27:11.568 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 09:27:11.597 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 09:27:11.597 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 09:27:11.597 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 09:27:11.597 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 09:27:11.597 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750382589,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:27:11.619 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:27:11.620 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[INFO ] 2025-06-20 09:27:11.634 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:27:11.634 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 09:27:11.634 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[INFO ] 2025-06-20 09:27:11.634 - [任务 16][MDM_TWPOS] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 09:27:11.641 - [任务 16][MDM_TWPOS] - Use existing batch read offset: {"MDM_TWPOS":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"_data":{"value":"826854B821000000022B022C0100296E5A10049C425F2A2138416DB70E62FD7473EBF146645F69640064685115C048108A26BFC170A30004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 09:27:11.719 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 09:27:11.719 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 09:27:11.719 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 09:27:11.720 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"_data":{"value":"826854B821000000022B022C0100296E5A10049C425F2A2138416DB70E62FD7473EBF146645F69640064685115C048108A26BFC170A30004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 09:27:11.720 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:27:11.921 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[TRACE] 2025-06-20 09:27:13.590 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6da17c63: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 09:27:14.195 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@6da17c63: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 09:27:36.426 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:27:36.430 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382831346 
[TRACE] 2025-06-20 09:27:36.431 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382831346 
[TRACE] 2025-06-20 09:27:36.431 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.431 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:27:36.431 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 5 ms 
[TRACE] 2025-06-20 09:27:36.431 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:27:36.437 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382831367 
[TRACE] 2025-06-20 09:27:36.437 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382831367 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.438 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:27:36.439 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:36.439 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382831351 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382831351 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.445 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 0 ms 
[TRACE] 2025-06-20 09:27:36.446 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 09:27:36.447 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 09:27:36.447 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 09:27:36.447 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.447 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 09:27:36.448 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 1 ms 
[TRACE] 2025-06-20 09:27:36.448 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 09:27:36.450 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382831359 
[TRACE] 2025-06-20 09:27:36.451 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382831359 
[TRACE] 2025-06-20 09:27:36.451 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 09:27:36.451 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 09:27:36.655 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:27:36.727 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 09:27:36.727 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 09:27:36.930 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 09:27:45.462 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 09:27:46.403 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@7386d3da 
[TRACE] 2025-06-20 09:27:46.403 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60e3cccb 
[TRACE] 2025-06-20 09:27:46.521 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 09:27:46.521 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:27:46.521 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 09:27:46.521 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 09:27:46.564 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:27:46.564 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:28:39.568 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 09:28:39.692 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 09:28:40.080 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-00e1f229-dd40-4fea-b3ea-f0fc7cd8cd94 complete, cost 424ms 
[TRACE] 2025-06-20 09:28:40.503 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-818fe074-c79f-4550-9538-5b43766184ee complete, cost 411ms 
[TRACE] 2025-06-20 09:28:40.933 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5ac93fe7-db39-492e-8d11-2d28283e1e63 complete, cost 404ms 
[INFO ] 2025-06-20 09:28:40.992 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 09:28:41.197 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 09:28:41.437 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 09:28:41.437 - [任务 16] - Task started 
[TRACE] 2025-06-20 09:28:41.469 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 09:28:41.469 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.470 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:28:41.471 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:28:41.472 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[INFO ] 2025-06-20 09:28:41.667 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 09:28:41.667 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 09:28:41.667 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 09:28:41.667 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:28:41.737 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:41.737 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 09:28:41.737 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 09:28:41.737 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 09:28:41.737 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:28:41.741 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:28:41.741 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 09:28:41.741 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 09:28:41.741 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-20 09:28:41.763 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 09:28:41.764 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:41.799 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:41.802 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:28:41.802 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[INFO ] 2025-06-20 09:28:41.847 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:28:41.847 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 09:28:41.883 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 09:28:41.883 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[INFO ] 2025-06-20 09:28:41.883 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:28:41.883 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[TRACE] 2025-06-20 09:28:41.888 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 09:28:41.888 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 09:28:41.888 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 09:28:41.893 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:41.895 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:28:41.909 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[TRACE] 2025-06-20 09:28:41.909 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:28:41.929 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 09:28:41.929 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[WARN ] 2025-06-20 09:28:42.752 - [任务 16][JS_MAIN][src=user_script]  - null 
[WARN ] 2025-06-20 09:28:42.752 - [任务 16][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:28:43.863 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7e6cfa83: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 09:28:44.469 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7e6cfa83: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 09:28:44.883 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 09:28:44.883 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:28:44.884 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 09:28:44.884 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 09:28:44.884 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[INFO ] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:44.889 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:28:44.891 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[TRACE] 2025-06-20 09:28:44.900 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 09:28:44.900 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:28:44.900 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:28:46.931 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 09:28:46.931 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:28:46.932 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 09:28:46.932 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 09:28:46.932 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 09:28:46.933 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 09:28:46.938 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 09:28:46.938 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 09:28:46.938 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 09:28:46.938 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 09:28:46.939 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 09:28:46.939 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750382920,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:28:46.939 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:28:46.948 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:28:46.948 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:28:47.149 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 09:28:49.000 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:28:49.000 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:29:46.091 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:29:46.100 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382921576 
[TRACE] 2025-06-20 09:29:46.100 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750382921576 
[TRACE] 2025-06-20 09:29:46.100 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.100 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:29:46.100 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 12 ms 
[TRACE] 2025-06-20 09:29:46.101 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:29:46.104 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-1fc46a18-a90c-45ae-94a3-6d94f4fa45f5 
[INFO ] 2025-06-20 09:29:46.104 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-1fc46a18-a90c-45ae-94a3-6d94f4fa45f5 
[INFO ] 2025-06-20 09:29:46.104 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:29:46.105 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-4fad42f3-7737-40bf-a825-541a82783a13 
[INFO ] 2025-06-20 09:29:46.105 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-4fad42f3-7737-40bf-a825-541a82783a13 
[INFO ] 2025-06-20 09:29:46.105 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.108 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.108 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:29:46.108 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:29:46.108 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382921643 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750382921643 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:29:46.112 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:29:46.115 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-23aac0f8-415a-459c-a343-5fbbf53e7c89 
[INFO ] 2025-06-20 09:29:46.115 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-23aac0f8-415a-459c-a343-5fbbf53e7c89 
[INFO ] 2025-06-20 09:29:46.115 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:29:46.117 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-0751aa67-2a47-415c-8092-e3706a5160ea 
[INFO ] 2025-06-20 09:29:46.117 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-0751aa67-2a47-415c-8092-e3706a5160ea 
[INFO ] 2025-06-20 09:29:46.117 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.119 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.119 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:29:46.119 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382921605 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750382921605 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:29:46.122 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:29:46.124 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-30583110-d2e4-49d8-8769-b24018b15b6a 
[INFO ] 2025-06-20 09:29:46.124 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-30583110-d2e4-49d8-8769-b24018b15b6a 
[INFO ] 2025-06-20 09:29:46.124 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:29:46.131 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-d233c007-4479-437c-8c7c-dfe391f84041 
[INFO ] 2025-06-20 09:29:46.131 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-d233c007-4479-437c-8c7c-dfe391f84041 
[INFO ] 2025-06-20 09:29:46.131 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.132 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.132 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:29:46.132 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 9 ms 
[TRACE] 2025-06-20 09:29:46.132 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 09:29:46.135 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 09:29:46.135 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 09:29:46.137 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.137 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 09:29:46.137 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:29:46.137 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 09:29:46.145 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382921753 
[TRACE] 2025-06-20 09:29:46.145 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750382921753 
[TRACE] 2025-06-20 09:29:46.145 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 09:29:46.145 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 09:29:46.145 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:29:46.169 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 09:29:46.374 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 09:29:51.658 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 09:29:52.665 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@75291170 
[TRACE] 2025-06-20 09:29:52.669 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56a1b4cd 
[TRACE] 2025-06-20 09:29:52.670 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 09:29:52.795 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:29:52.795 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 09:29:52.796 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 09:29:52.843 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:29:52.843 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:30:12.218 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 09:30:12.218 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 09:30:12.734 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-fddaf35d-154c-4158-8bca-381410c184a8 complete, cost 431ms 
[TRACE] 2025-06-20 09:30:13.158 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b0d6db4d-be2a-4e0c-ab4b-42dc77d397ab complete, cost 412ms 
[TRACE] 2025-06-20 09:30:13.638 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3f374fc8-5f0c-4b64-8726-ead5c2d34fd1 complete, cost 454ms 
[INFO ] 2025-06-20 09:30:13.697 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 09:30:13.899 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 09:30:14.179 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 09:30:14.241 - [任务 16] - Task started 
[TRACE] 2025-06-20 09:30:14.241 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.241 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 09:30:14.241 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.242 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.243 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 09:30:14.243 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.243 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.244 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 09:30:14.434 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[INFO ] 2025-06-20 09:30:14.434 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:30:14.434 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 09:30:14.434 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 09:30:14.460 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:30:14.461 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750383003,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:14.502 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 09:30:14.502 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 09:30:14.502 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 09:30:14.502 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-20 09:30:14.522 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 09:30:14.522 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750383003,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:14.554 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:30:14.554 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 09:30:14.554 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 09:30:14.558 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 09:30:14.558 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750383003,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:14.564 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:30:14.572 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[INFO ] 2025-06-20 09:30:14.575 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 09:30:14.575 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 09:30:14.575 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 09:30:14.575 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 09:30:14.591 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 09:30:14.595 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 09:30:14.595 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 09:30:14.621 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750383014,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:14.621 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:30:14.621 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[TRACE] 2025-06-20 09:30:14.656 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 09:30:14.676 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 09:30:14.676 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-20 09:30:14.696 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 09:30:14.696 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[WARN ] 2025-06-20 09:30:15.535 - [任务 16][JS_MAIN][src=user_script]  - null 
[WARN ] 2025-06-20 09:30:15.735 - [任务 16][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 09:30:16.744 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7440a63c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 09:30:17.109 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7440a63c: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 09:30:17.649 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 09:30:17.649 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:30:17.650 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 09:30:17.650 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 09:30:17.650 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[INFO ] 2025-06-20 09:30:17.653 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 09:30:17.653 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 09:30:17.653 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 09:30:17.653 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 09:30:17.653 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 09:30:17.654 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750383003,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:17.654 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:30:17.656 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[TRACE] 2025-06-20 09:30:17.656 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 09:30:17.664 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:30:17.664 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:30:19.702 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 09:30:19.704 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 09:30:19.704 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 09:30:19.705 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 09:30:19.705 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 09:30:19.707 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 09:30:19.717 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 09:30:19.717 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 09:30:19.717 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 09:30:19.717 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 09:30:19.718 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 09:30:19.718 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750383014,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 09:30:19.718 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 09:30:19.725 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 09:30:19.729 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:30:19.729 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 09:30:21.796 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 09:30:21.996 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[WARN ] 2025-06-20 09:49:26.957 - [任务 16][JS_MAIN][src=user_script]  - {_id=685115c048108a26bfc170a3, INVC_LINE_NBR=2, INVC_NBR=S230125   , ALLOW_TRADE_IND=null, APX_SEQ=2, BOOK_WT=0.0, CLOSE_RTE=0.0, CNSLD_GV_TXN_ID=null, CNSLD_TXN_ID=null, COUPN_AMT=null, COUPN_CDE=null, CO_LINE_ID=F46-01365AK-2, CT_ID=null, CT_LINE=null, CT_LINE_NBR=null, DISC_CDE=TM, EST_REFER_GAIN_SD_AMT=null, FREE_COUPN_AMT=null, GIFT_CATG_NBR=null, GOLD_COST=6500.25, GOLD_RTE=null, GROSS_AMT=45000.0, GROSS_WT=80, GRP_NBR=1, INDIRCT_COST=0.0, INVNT_COST=12800.5, INVNT_ID=10435317607, INVNT_PRICE=48000.0, LABOR_CHRG=null, LABOR_COST=6200.75, MATRL_COST=0.0, MBR_EXTRA_DISC_AMT=0.0, MODEL_NBR=null, NET_AMT=45000.0, NET_PAY_AMT=null, PHYSL_WT=78, PRT_SOQ_IND=null, QTY=1, RETURN_IND=null, RETURN_INVC_LINE_NBR=null, RETURN_INVC_NBR=null, SOLD_AMT=45000.0, SP_GOOD_IND=N, STD_LABOR_PRICE=null, STD_PRICE=48000.0, TXN_CDE=SDF, USE_SD_IND=N, VAT_APPL_AMT=null, VCHR_AMT=null, VCHR_CDE=null, VIP_POINT=null, VP_SALES_AMT=null, VP_SALES_QTY=null} 
[TRACE] 2025-06-20 09:50:45.256 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750383014404 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750383014404 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 9 ms 
[TRACE] 2025-06-20 09:50:45.264 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750383014345 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750383014345 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:50:45.269 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 09:50:45.273 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750383014346 
[TRACE] 2025-06-20 09:50:45.273 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750383014346 
[TRACE] 2025-06-20 09:50:45.273 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.273 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 09:50:45.273 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 3 ms 
[TRACE] 2025-06-20 09:50:45.274 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 09:50:45.276 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-b517ece0-1af5-4560-b430-66d9751f421c 
[INFO ] 2025-06-20 09:50:45.277 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-b517ece0-1af5-4560-b430-66d9751f421c 
[INFO ] 2025-06-20 09:50:45.277 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:50:45.279 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-10b596a2-9d64-4fcf-bbd8-8ef0f78f6994 
[INFO ] 2025-06-20 09:50:45.279 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-10b596a2-9d64-4fcf-bbd8-8ef0f78f6994 
[INFO ] 2025-06-20 09:50:45.279 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.281 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.282 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 09:50:45.282 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:50:45.282 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 09:50:45.285 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-ae235da2-2d5f-4d58-86e8-1f19865a567d 
[INFO ] 2025-06-20 09:50:45.285 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-ae235da2-2d5f-4d58-86e8-1f19865a567d 
[INFO ] 2025-06-20 09:50:45.285 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:50:45.288 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-7e10e0f9-8034-4c95-a508-9961d0423fb8 
[INFO ] 2025-06-20 09:50:45.288 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-7e10e0f9-8034-4c95-a508-9961d0423fb8 
[INFO ] 2025-06-20 09:50:45.288 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.289 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.290 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 09:50:45.290 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 7 ms 
[TRACE] 2025-06-20 09:50:45.290 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 09:50:45.293 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-66a2099c-b627-47a9-9595-bbf722df7b4c 
[INFO ] 2025-06-20 09:50:45.294 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-66a2099c-b627-47a9-9595-bbf722df7b4c 
[INFO ] 2025-06-20 09:50:45.294 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 09:50:45.297 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-6194f60a-efab-420f-8565-b36ec58d25f5 
[INFO ] 2025-06-20 09:50:45.297 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-6194f60a-efab-420f-8565-b36ec58d25f5 
[INFO ] 2025-06-20 09:50:45.297 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.298 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.298 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 09:50:45.298 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:50:45.298 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 09:50:45.298 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 09:50:45.302 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 09:50:45.302 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.302 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 09:50:45.302 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 4 ms 
[TRACE] 2025-06-20 09:50:45.302 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 09:50:45.310 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750383014501 
[TRACE] 2025-06-20 09:50:45.311 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750383014501 
[TRACE] 2025-06-20 09:50:45.311 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 09:50:45.311 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 09:50:45.516 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 8 ms 
[TRACE] 2025-06-20 09:50:45.718 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 09:50:46.100 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 09:50:46.100 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 09:50:53.643 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 09:50:54.585 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@67ddb8bb 
[TRACE] 2025-06-20 09:50:54.586 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b558bbb 
[TRACE] 2025-06-20 09:50:54.709 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 09:50:54.709 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 09:50:54.710 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 09:50:54.710 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 09:50:54.755 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 09:50:54.755 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:03:31.010 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 11:03:31.177 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 11:03:31.655 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-0899a15c-0d45-4d08-9eb8-407aeb48697c complete, cost 544ms 
[TRACE] 2025-06-20 11:03:32.112 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-31abf314-0cd1-4533-b5af-10eaad1ea27b complete, cost 444ms 
[TRACE] 2025-06-20 11:03:32.604 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d31efdf9-6d77-4df1-9eaa-b0ceca479834 complete, cost 464ms 
[INFO ] 2025-06-20 11:03:32.695 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 11:03:32.695 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 11:03:33.232 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 11:03:33.232 - [任务 16] - Task started 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.269 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:03:33.270 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:03:33.270 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.270 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.270 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.271 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.272 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 11:03:33.273 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[INFO ] 2025-06-20 11:03:33.489 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 11:03:33.489 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 11:03:33.489 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 11:03:33.489 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:03:33.561 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:33.564 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:03:33.564 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 11:03:33.564 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 11:03:33.564 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-20 11:03:33.592 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 11:03:33.592 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:33.631 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:03:33.631 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[INFO ] 2025-06-20 11:03:33.712 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 11:03:33.712 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 11:03:33.712 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 11:03:33.712 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:03:33.713 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:03:33.713 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 11:03:33.747 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 11:03:33.747 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:33.823 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:03:33.823 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 11:03:33.823 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 11:03:33.823 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 11:03:33.834 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 11:03:33.834 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:33.835 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 11:03:33.849 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:03:33.849 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[TRACE] 2025-06-20 11:03:33.894 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[TRACE] 2025-06-20 11:03:33.894 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 11:03:33.949 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 11:03:33.949 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[WARN ] 2025-06-20 11:03:34.620 - [任务 16][JS_MAIN][src=user_script]  - null 
[WARN ] 2025-06-20 11:03:34.620 - [任务 16][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:03:34.825 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2866d209: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 11:03:35.221 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2866d209: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 11:03:36.251 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 11:03:36.252 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:03:36.253 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 11:03:36.256 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 11:03:36.257 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[TRACE] 2025-06-20 11:03:36.261 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[INFO ] 2025-06-20 11:03:36.261 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 11:03:36.266 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 11:03:36.266 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 11:03:36.270 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 11:03:36.270 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 11:03:36.274 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:36.274 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:03:36.274 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:03:36.274 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:03:36.274 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 11:03:38.803 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 11:03:38.803 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:03:38.803 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 11:03:38.803 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 11:03:38.803 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 11:03:38.806 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750388607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:03:38.809 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:03:38.817 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 11:03:38.817 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:03:39.023 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:03:40.866 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:03:40.866 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[WARN ] 2025-06-20 11:06:22.144 - [任务 16][JS_MAIN][src=user_script]  - {_id=68466fd436409d3d9426218c, INVC_LINE_NBR=1, INVC_NBR=S230124   , ALLOW_TRADE_IND=null, APX_SEQ=1, BOOK_WT=0.0, CLOSE_RTE=0.0, CNSLD_GV_TXN_ID=null, CNSLD_TXN_ID=null, COUPN_AMT=null, COUPN_CDE=null, CO_LINE_ID=F46-01365AK-1, CT_ID=null, CT_LINE=null, CT_LINE_NBR=null, DISC_CDE=TM, EST_REFER_GAIN_SD_AMT=null, FREE_COUPN_AMT=null, GIFT_CATG_NBR=null, GOLD_COST=5700.12, GOLD_RTE=null, GROSS_AMT=32070.0, GROSS_WT=70, GRP_NBR=1, INDIRCT_COST=0.0, INVNT_COST=11592.46, INVNT_ID=10435317606, INVNT_PRICE=35800.0, LABOR_CHRG=null, LABOR_COST=5892.34, MATRL_COST=0.0, MBR_EXTRA_DISC_AMT=0.0, MODEL_NBR=null, NET_AMT=32070.0, NET_PAY_AMT=null, PHYSL_WT=69, PRT_SOQ_IND=null, QTY=1, RETURN_IND=null, RETURN_INVC_LINE_NBR=null, RETURN_INVC_NBR=null, SOLD_AMT=32070.0, SP_GOOD_IND=N, STD_LABOR_PRICE=null, STD_PRICE=35800.0, TXN_CDE=SDF, USE_SD_IND=N, VAT_APPL_AMT=null, VCHR_AMT=null, VCHR_CDE=null, VIP_POINT=null, VP_SALES_AMT=null, VP_SALES_QTY=null} 
[WARN ] 2025-06-20 11:09:04.821 - [任务 16][JS_MAIN][src=user_script]  - {_id=68466fd436409d3d9426218c, INVC_LINE_NBR=1, INVC_NBR=S230126   , ALLOW_TRADE_IND=null, APX_SEQ=1, BOOK_WT=0.0, CLOSE_RTE=0.0, CNSLD_GV_TXN_ID=null, CNSLD_TXN_ID=null, COUPN_AMT=null, COUPN_CDE=null, CO_LINE_ID=F46-01365AK-1, CT_ID=null, CT_LINE=null, CT_LINE_NBR=null, DISC_CDE=TM, EST_REFER_GAIN_SD_AMT=null, FREE_COUPN_AMT=null, GIFT_CATG_NBR=null, GOLD_COST=5700.12, GOLD_RTE=null, GROSS_AMT=32070.0, GROSS_WT=70, GRP_NBR=1, INDIRCT_COST=0.0, INVNT_COST=11592.46, INVNT_ID=10435317606, INVNT_PRICE=35800.0, LABOR_CHRG=null, LABOR_COST=5892.34, MATRL_COST=0.0, MBR_EXTRA_DISC_AMT=0.0, MODEL_NBR=null, NET_AMT=32070.0, NET_PAY_AMT=null, PHYSL_WT=69, PRT_SOQ_IND=null, QTY=1, RETURN_IND=null, RETURN_INVC_LINE_NBR=null, RETURN_INVC_NBR=null, SOLD_AMT=32070.0, SP_GOOD_IND=N, STD_LABOR_PRICE=null, STD_PRICE=35800.0, TXN_CDE=SDF, USE_SD_IND=N, VAT_APPL_AMT=null, VCHR_AMT=null, VCHR_CDE=null, VIP_POINT=null, VP_SALES_AMT=null, VP_SALES_QTY=null} 
[TRACE] 2025-06-20 11:11:07.509 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750388613394 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750388613394 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:11:07.510 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:11:07.512 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-a0266ed9-abb6-4c39-8a6c-a9365c6638f4 
[INFO ] 2025-06-20 11:11:07.512 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-a0266ed9-abb6-4c39-8a6c-a9365c6638f4 
[INFO ] 2025-06-20 11:11:07.512 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:11:07.514 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-782cafda-610f-4346-bb28-2f9e4488bfc2 
[INFO ] 2025-06-20 11:11:07.514 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-782cafda-610f-4346-bb28-2f9e4488bfc2 
[INFO ] 2025-06-20 11:11:07.514 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.516 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.516 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:11:07.516 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:11:07.516 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750388613394 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750388613394 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:11:07.523 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:11:07.525 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6339d79f-58f8-4ca3-b9fc-3820489076ac 
[INFO ] 2025-06-20 11:11:07.526 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6339d79f-58f8-4ca3-b9fc-3820489076ac 
[INFO ] 2025-06-20 11:11:07.526 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:11:07.528 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-8c5e32e4-aaaf-4301-a1b0-0b00735635a2 
[INFO ] 2025-06-20 11:11:07.528 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-8c5e32e4-aaaf-4301-a1b0-0b00735635a2 
[INFO ] 2025-06-20 11:11:07.528 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.529 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.529 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:11:07.529 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:11:07.529 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:11:07.532 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750388613449 
[TRACE] 2025-06-20 11:11:07.532 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750388613449 
[TRACE] 2025-06-20 11:11:07.532 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.533 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:11:07.533 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:11:07.533 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:11:07.536 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-541b8ce2-d476-474b-aa7d-125b1b001040 
[INFO ] 2025-06-20 11:11:07.536 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-541b8ce2-d476-474b-aa7d-125b1b001040 
[INFO ] 2025-06-20 11:11:07.536 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:11:07.538 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-69729414-6276-4c9b-a444-3c1f035697c0 
[INFO ] 2025-06-20 11:11:07.538 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-69729414-6276-4c9b-a444-3c1f035697c0 
[INFO ] 2025-06-20 11:11:07.538 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.539 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.539 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:11:07.540 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:11:07.540 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 11:11:07.540 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 11:11:07.541 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 11:11:07.543 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.543 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 11:11:07.543 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:11:07.543 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 11:11:07.552 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750388613558 
[TRACE] 2025-06-20 11:11:07.553 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750388613558 
[TRACE] 2025-06-20 11:11:07.553 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 11:11:07.553 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 11:11:07.757 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 9 ms 
[TRACE] 2025-06-20 11:11:07.965 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 11:11:08.170 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 11:11:08.302 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 11:11:14.990 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 11:11:15.879 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@2f077736 
[TRACE] 2025-06-20 11:11:15.879 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2cfb8f75 
[TRACE] 2025-06-20 11:11:15.998 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 11:11:15.998 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:11:15.998 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 11:11:15.998 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 11:11:16.039 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:11:16.043 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:14:05.529 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 11:14:05.734 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 11:14:06.225 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-d996d311-3610-4772-864c-2b2f7b2a44e1 complete, cost 544ms 
[TRACE] 2025-06-20 11:14:06.732 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-00a74341-94c4-47ed-a4a8-6bcd7d6bd008 complete, cost 493ms 
[TRACE] 2025-06-20 11:14:07.200 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-f3d429b8-5240-464b-b5a7-f9da1c8d6f6e complete, cost 440ms 
[INFO ] 2025-06-20 11:14:07.293 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 11:14:07.293 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 11:14:07.791 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 11:14:07.791 - [任务 16] - Task started 
[TRACE] 2025-06-20 11:14:07.828 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.828 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.828 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 11:14:07.829 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 11:14:07.830 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 11:14:07.831 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[INFO ] 2025-06-20 11:14:08.079 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 11:14:08.079 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 11:14:08.079 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:08.079 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:14:08.129 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-20 11:14:08.132 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 11:14:08.141 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 11:14:08.141 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 11:14:08.141 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:08.141 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:14:08.205 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:08.205 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:14:08.206 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[INFO ] 2025-06-20 11:14:08.229 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[INFO ] 2025-06-20 11:14:08.229 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:14:08.229 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 11:14:08.229 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-06-20 11:14:08.229 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:08.229 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:14:08.257 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 11:14:08.257 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:08.265 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:14:08.265 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[TRACE] 2025-06-20 11:14:08.313 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 11:14:08.314 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 11:14:08.314 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-20 11:14:08.348 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:14:08.348 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 11:14:08.348 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 11:14:08.348 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:08.357 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:08.562 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[WARN ] 2025-06-20 11:14:09.124 - [任务 16][JS_MAIN][src=user_script]  - null 
[WARN ] 2025-06-20 11:14:09.124 - [任务 16][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:14:10.247 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@85bd440: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 11:14:10.748 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@85bd440: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 11:14:11.276 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 11:14:11.276 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:14:11.276 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 11:14:11.276 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 11:14:11.276 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[INFO ] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:11.279 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:11.286 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[TRACE] 2025-06-20 11:14:11.286 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 11:14:11.293 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:11.293 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:14:13.333 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 11:14:13.333 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:14:13.333 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 11:14:13.333 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 11:14:13.333 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750389247,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:13.341 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:13.349 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:13.353 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:14:13.557 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 11:14:15.402 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:15.403 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:14:25.204 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389248024 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750389248024 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:14:25.209 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:14:25.216 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389247977 
[TRACE] 2025-06-20 11:14:25.216 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750389247977 
[TRACE] 2025-06-20 11:14:25.216 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.216 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:14:25.216 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 7 ms 
[TRACE] 2025-06-20 11:14:25.217 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[INFO ] 2025-06-20 11:14:25.220 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-6b9742ed-09bb-4456-8b35-54aaf1526cfe 
[INFO ] 2025-06-20 11:14:25.220 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-6b9742ed-09bb-4456-8b35-54aaf1526cfe 
[INFO ] 2025-06-20 11:14:25.221 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:14:25.223 - [任务 16][JS_1][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-ce2fe74b-4187-44b5-b1e8-7f35bf9f7a04 
[INFO ] 2025-06-20 11:14:25.223 - [任务 16][JS_1][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-ce2fe74b-4187-44b5-b1e8-7f35bf9f7a04 
[INFO ] 2025-06-20 11:14:25.223 - [任务 16][JS_1][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-ef5593e5-b603-4c98-97e0-14f0eab89681-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.223 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.223 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:14:25.224 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 7 ms 
[TRACE] 2025-06-20 11:14:25.224 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389248024 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750389248024 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:14:25.226 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[INFO ] 2025-06-20 11:14:25.233 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-e29b58a4-1980-4c9d-b8b5-597bc2cef3e4 
[INFO ] 2025-06-20 11:14:25.233 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-e29b58a4-1980-4c9d-b8b5-597bc2cef3e4 
[INFO ] 2025-06-20 11:14:25.233 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:14:25.234 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-6a18395e-519b-44bd-9152-4fe6cb3d4c30 
[INFO ] 2025-06-20 11:14:25.234 - [任务 16][JS_MAIN][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-6a18395e-519b-44bd-9152-4fe6cb3d4c30 
[INFO ] 2025-06-20 11:14:25.234 - [任务 16][JS_MAIN][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-71e48ee7-176e-4214-9388-8b249fc284b3-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.235 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.235 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:14:25.235 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 8 ms 
[TRACE] 2025-06-20 11:14:25.235 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[INFO ] 2025-06-20 11:14:25.237 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo-5de9cea5-f07f-492e-9e42-39ef8ed102be 
[INFO ] 2025-06-20 11:14:25.237 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo-5de9cea5-f07f-492e-9e42-39ef8ed102be 
[INFO ] 2025-06-20 11:14:25.237 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3f99bd4403278dfff5ea] schema data cleaned 
[INFO ] 2025-06-20 11:14:25.239 - [任务 16][JS_2][src=user_script]  - PDK connector node stopped: ScriptExecutor-local_mongo - Copy-cd317603-7a35-4931-8bc8-79bbeb2e59f1 
[INFO ] 2025-06-20 11:14:25.239 - [任务 16][JS_2][src=user_script]  - PDK connector node released: ScriptExecutor-local_mongo - Copy-cd317603-7a35-4931-8bc8-79bbeb2e59f1 
[INFO ] 2025-06-20 11:14:25.239 - [任务 16][JS_2][src=user_script]  - [ScriptExecutorsManager-68511c583717ee6ee2007d97-a2ed499f-6c69-4e02-bfbc-f855705283f7-684a3fb2bd4403278dfff5f3] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.240 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.240 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:14:25.240 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 5 ms 
[TRACE] 2025-06-20 11:14:25.240 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 11:14:25.240 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 11:14:25.244 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 11:14:25.244 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.244 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 11:14:25.245 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 4 ms 
[TRACE] 2025-06-20 11:14:25.245 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 11:14:25.254 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750389248117 
[TRACE] 2025-06-20 11:14:25.254 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750389248117 
[TRACE] 2025-06-20 11:14:25.254 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 11:14:25.255 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 11:14:25.255 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 9 ms 
[TRACE] 2025-06-20 11:14:25.407 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 11:14:25.407 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 11:14:25.611 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 11:14:31.291 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 11:14:32.194 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@778ebddb 
[TRACE] 2025-06-20 11:14:32.194 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7dc3b34 
[TRACE] 2025-06-20 11:14:32.312 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 11:14:32.312 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:14:32.312 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 11:14:32.312 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 11:14:32.375 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:14:32.375 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:14:36.337 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 11:14:36.337 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[TRACE] 2025-06-20 11:14:36.904 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-5e23d608-a8a7-4d23-bdb6-a3099c8522df complete, cost 441ms 
[TRACE] 2025-06-20 11:14:37.372 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-371daf30-5ebb-47f0-9717-07a65a89a81a complete, cost 452ms 
[TRACE] 2025-06-20 11:14:37.871 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-b77d54b4-b692-42cf-8040-b398b7ec0788 complete, cost 468ms 
[INFO ] 2025-06-20 11:14:37.932 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 11:14:38.136 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 11:14:38.488 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 11:14:38.557 - [任务 16] - Task started 
[TRACE] 2025-06-20 11:14:38.557 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.557 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.557 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.558 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.559 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.559 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.559 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:14:38.559 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:14:38.559 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 11:14:38.560 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[INFO ] 2025-06-20 11:14:38.790 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:14:38.790 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 11:14:38.790 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:38.790 - [任务 16][MDM_TWPOS] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:14:38.815 - [任务 16][MDM_TWPOS] - Use existing stream offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[INFO ] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[INFO ] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-06-20 11:14:38.892 - [任务 16][TWPOS_PS_INVC_TXN] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 11:14:38.919 - [任务 16][TWPOS_PS_INVC] - Use existing stream offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:38.921 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing stream offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:38.932 - [任务 16][MDM_TWPOS] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:14:38.933 - [任务 16][MDM_TWPOS] - Initial sync started 
[INFO ] 2025-06-20 11:14:38.933 - [任务 16][MDM_TWPOS] - Starting batch read from table: MDM_TWPOS 
[TRACE] 2025-06-20 11:14:38.933 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS is going to be initial synced 
[TRACE] 2025-06-20 11:14:38.937 - [任务 16][MDM_TWPOS] - Query snapshot row size completed: MDM_TWPOS(ed9c0b12-c8e2-4220-8fd7-e730ff199180) 
[INFO ] 2025-06-20 11:14:38.937 - [任务 16][MDM_TWPOS] - Table MDM_TWPOS has been completed batch read 
[TRACE] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Initial sync completed 
[INFO ] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:38.938 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:38.951 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[INFO ] 2025-06-20 11:14:38.951 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:14:38.951 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 11:14:39.006 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[INFO ] 2025-06-20 11:14:39.006 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from 1 tables 
[INFO ] 2025-06-20 11:14:39.006 - [任务 16][TWPOS_PS_INVC] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 11:14:39.006 - [任务 16] - Node[TWPOS_PS_INVC_TXN] is waiting for running 
[TRACE] 2025-06-20 11:14:39.006 - [任务 16] - Node[TWPOS_PS_INVC] is waiting for running 
[TRACE] 2025-06-20 11:14:39.034 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 11:14:39.034 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 11:14:39.034 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[WARN ] 2025-06-20 11:14:39.867 - [任务 16][JS_MAIN][src=user_script]  - null 
[WARN ] 2025-06-20 11:14:39.867 - [任务 16][JS_MAIN][src=user_script]  - null 
[TRACE] 2025-06-20 11:14:41.086 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@51cacd24: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 11:14:41.466 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@51cacd24: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 11:14:42.010 - [任务 16] - Node[MDM_TWPOS] finish, notify next layer to run 
[TRACE] 2025-06-20 11:14:42.011 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:14:42.012 - [任务 16][TWPOS_PS_INVC] - Initial sync started 
[INFO ] 2025-06-20 11:14:42.012 - [任务 16][TWPOS_PS_INVC] - Starting batch read from table: TWPOS_PS_INVC 
[TRACE] 2025-06-20 11:14:42.012 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC is going to be initial synced 
[TRACE] 2025-06-20 11:14:42.014 - [任务 16][TWPOS_PS_INVC] - Query snapshot row size completed: TWPOS_PS_INVC(51a10cba-3a5a-4dfd-9631-27f55d73882d) 
[INFO ] 2025-06-20 11:14:42.014 - [任务 16][TWPOS_PS_INVC] - Table TWPOS_PS_INVC has been completed batch read 
[TRACE] 2025-06-20 11:14:42.014 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[INFO ] 2025-06-20 11:14:42.015 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 11:14:42.015 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:42.015 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 11:14:42.015 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:42.015 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:42.023 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[TRACE] 2025-06-20 11:14:42.023 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:42.023 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:14:44.068 - [任务 16] - Node[TWPOS_PS_INVC] finish, notify next layer to run 
[TRACE] 2025-06-20 11:14:44.069 - [任务 16] - Next layer have been notified: [null] 
[TRACE] 2025-06-20 11:14:44.069 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync started 
[INFO ] 2025-06-20 11:14:44.069 - [任务 16][TWPOS_PS_INVC_TXN] - Starting batch read from table: TWPOS_PS_INVC_TXN 
[TRACE] 2025-06-20 11:14:44.072 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN is going to be initial synced 
[TRACE] 2025-06-20 11:14:44.072 - [任务 16][TWPOS_PS_INVC_TXN] - Query snapshot row size completed: TWPOS_PS_INVC_TXN(f0e184e1-55c7-4967-8c90-a748a579996a) 
[INFO ] 2025-06-20 11:14:44.081 - [任务 16][TWPOS_PS_INVC_TXN] - Table TWPOS_PS_INVC_TXN has been completed batch read 
[TRACE] 2025-06-20 11:14:44.081 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[INFO ] 2025-06-20 11:14:44.081 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 11:14:44.082 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 11:14:44.082 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 11:14:44.082 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"cdcOffset":1750389272,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2025-06-20 11:14:44.082 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:14:44.094 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[TRACE] 2025-06-20 11:14:44.094 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:44.095 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-20 11:14:46.217 - [任务 16][MDM_NEW_TWPOS] - Process after table "MDM_NEW_TWPOS" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 11:14:46.217 - [任务 16][MDM_NEW_TWPOS] - Process after all table(s) initial sync are finished，table number: 1 
[WARN ] 2025-06-20 11:15:54.139 - [任务 16][JS_MAIN][src=user_script]  - {_id=68466fd436409d3d9426218c, INVC_LINE_NBR=1, INVC_NBR=S230126   , ALLOW_TRADE_IND=null, APX_SEQ=1, BOOK_WT=0.0, CLOSE_RTE=0.0, CNSLD_GV_TXN_ID=null, CNSLD_TXN_ID=null, COUPN_AMT=null, COUPN_CDE=null, CO_LINE_ID=F46-01365AK-1, CT_ID=null, CT_LINE=null, CT_LINE_NBR=null, DISC_CDE=TM, EST_REFER_GAIN_SD_AMT=null, FREE_COUPN_AMT=null, GIFT_CATG_NBR=null, GOLD_COST=5700.13, GOLD_RTE=null, GROSS_AMT=32070.0, GROSS_WT=70, GRP_NBR=1, INDIRCT_COST=0.0, INVNT_COST=11592.46, INVNT_ID=10435317606, INVNT_PRICE=35800.0, LABOR_CHRG=null, LABOR_COST=5892.34, MATRL_COST=0.0, MBR_EXTRA_DISC_AMT=0.0, MODEL_NBR=null, NET_AMT=32070.0, NET_PAY_AMT=null, PHYSL_WT=69, PRT_SOQ_IND=null, QTY=1, RETURN_IND=null, RETURN_INVC_LINE_NBR=null, RETURN_INVC_NBR=null, SOLD_AMT=32070.0, SP_GOOD_IND=N, STD_LABOR_PRICE=null, STD_PRICE=35800.0, TXN_CDE=SDF, USE_SD_IND=N, VAT_APPL_AMT=null, VCHR_AMT=null, VCHR_CDE=null, VIP_POINT=null, VP_SALES_AMT=null, VP_SALES_QTY=null} 
[WARN ] 2025-06-20 11:20:08.939 - [任务 16][JS_MAIN][src=user_script]  - {_id=68466fd436409d3d9426218c, INVC_LINE_NBR=1, INVC_NBR=S230127   , ALLOW_TRADE_IND=null, APX_SEQ=1, BOOK_WT=0.0, CLOSE_RTE=0.0, CNSLD_GV_TXN_ID=null, CNSLD_TXN_ID=null, COUPN_AMT=null, COUPN_CDE=null, CO_LINE_ID=F46-01365AK-1, CT_ID=null, CT_LINE=null, CT_LINE_NBR=null, DISC_CDE=TM, EST_REFER_GAIN_SD_AMT=null, FREE_COUPN_AMT=null, GIFT_CATG_NBR=null, GOLD_COST=5700.13, GOLD_RTE=null, GROSS_AMT=32070.0, GROSS_WT=70, GRP_NBR=1, INDIRCT_COST=0.0, INVNT_COST=11592.46, INVNT_ID=10435317606, INVNT_PRICE=35800.0, LABOR_CHRG=null, LABOR_COST=5892.34, MATRL_COST=0.0, MBR_EXTRA_DISC_AMT=0.0, MODEL_NBR=null, NET_AMT=32070.0, NET_PAY_AMT=null, PHYSL_WT=69, PRT_SOQ_IND=null, QTY=1, RETURN_IND=null, RETURN_INVC_LINE_NBR=null, RETURN_INVC_NBR=null, SOLD_AMT=32070.0, SP_GOOD_IND=N, STD_LABOR_PRICE=null, STD_PRICE=35800.0, TXN_CDE=SDF, USE_SD_IND=N, VAT_APPL_AMT=null, VCHR_AMT=null, VCHR_CDE=null, VIP_POINT=null, VP_SALES_AMT=null, VP_SALES_QTY=null} 
[TRACE] 2025-06-20 11:44:09.739 - [任务 16] - Task initialization... 
[TRACE] 2025-06-20 11:44:09.745 - [任务 16] - Start task milestones: 68511c583717ee6ee2007d97(任务 16) 
[WARN ] 2025-06-20 11:44:38.938 - [任务 16] - Stop task failed because of task lock, will ignored 
[WARN ] 2025-06-20 11:45:27.273 - [任务 16] - Stop task failed because of task lock, will ignored 
[WARN ] 2025-06-20 11:45:57.109 - [任务 16] - Stop task failed because of task lock, will ignored 
[TRACE] 2025-06-20 11:46:20.335 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-3eb4e912-57d8-490d-a8f2-dc34c411768b complete, cost 130169ms 
[TRACE] 2025-06-20 11:46:20.743 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-7ceebc07-361c-45fa-b129-a7245a5c5ad6 complete, cost 380ms 
[TRACE] 2025-06-20 11:46:21.114 - [任务 16] - load tapTable task 68511c583717ee6ee2007d96-8b6b49c5-e547-4d18-8783-a3fd56f6ea13 complete, cost 326ms 
[INFO ] 2025-06-20 11:46:21.191 - [任务 16] - Loading table structure completed 
[TRACE] 2025-06-20 11:46:21.289 - [任务 16] - Node performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[TRACE] 2025-06-20 11:46:21.289 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 11:46:21.348 - [任务 16] - Task started 
[TRACE] 2025-06-20 11:46:21.348 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.348 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.348 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.349 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.350 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.351 - [任务 16][JS_1] - Node js_processor(JS_1: ef5593e5-b603-4c98-97e0-14f0eab89681) enable batch process 
[TRACE] 2025-06-20 11:46:21.351 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.351 - [任务 16][JS_MAIN] - Node js_processor(JS_MAIN: 71e48ee7-176e-4214-9388-8b249fc284b3) enable batch process 
[TRACE] 2025-06-20 11:46:21.351 - [任务 16][JS_2] - Node js_processor(JS_2: a2ed499f-6c69-4e02-bfbc-f855705283f7) enable batch process 
[TRACE] 2025-06-20 11:46:21.352 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] start preload schema,table counts: 1 
[TRACE] 2025-06-20 11:46:21.352 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.352 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] start preload schema,table counts: 4 
[TRACE] 2025-06-20 11:46:21.352 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:46:21.353 - [任务 16][主从合并] - Node merge_table_processor(主从合并: 4e21e0a0-4471-48d9-a30f-ecf8078ab932) enable batch process 
[TRACE] 2025-06-20 11:46:21.353 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
    ->增强JS(a2ed499f-6c69-4e02-bfbc-f855705283f7)
} 
[TRACE] 2025-06-20 11:46:21.353 - [任务 16][主从合并] - 
Merge lookup relation{
  增强JS(71e48ee7-176e-4214-9388-8b249fc284b3)
    ->增强JS(ef5593e5-b603-4c98-97e0-14f0eab89681)
} 
[TRACE] 2025-06-20 11:46:21.386 - [任务 16][主从合并] - Create merge cache, node id: a2ed499f-6c69-4e02-bfbc-f855705283f7, imap name: 802081887, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 11:46:21.412 - [任务 16][主从合并] - Create merge cache, node id: ef5593e5-b603-4c98-97e0-14f0eab89681, imap name: -983984999, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_355', table='null', ttlDay=0] 
[TRACE] 2025-06-20 11:46:21.412 - [任务 16][主从合并] - Merge table processor lookup thread num: 8 
[TRACE] 2025-06-20 11:46:21.412 - [任务 16][主从合并] - Merge table processor handle update join key thread num: 4 
[INFO ] 2025-06-20 11:46:21.452 - [任务 16][TWPOS_PS_INVC] - Source connector(TWPOS_PS_INVC) initialization completed 
[TRACE] 2025-06-20 11:46:21.452 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" read batch size: 100 
[TRACE] 2025-06-20 11:46:21.452 - [任务 16][TWPOS_PS_INVC] - Source node "TWPOS_PS_INVC" event queue capacity: 200 
[INFO ] 2025-06-20 11:46:21.458 - [任务 16][TWPOS_PS_INVC] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 11:46:21.458 - [任务 16][TWPOS_PS_INVC] - Use existing batch read offset: {"TWPOS_PS_INVC":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"_data":{"value":"826854D358000000022B022C0100296E5A1004B9BC4098C3E340C19D111B664D1CDC1C46645F69640064685115C048108A26BFC170A10004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.527 - [任务 16][TWPOS_PS_INVC_TXN] - Source connector(TWPOS_PS_INVC_TXN) initialization completed 
[TRACE] 2025-06-20 11:46:21.527 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" read batch size: 100 
[TRACE] 2025-06-20 11:46:21.527 - [任务 16][TWPOS_PS_INVC_TXN] - Source node "TWPOS_PS_INVC_TXN" event queue capacity: 200 
[INFO ] 2025-06-20 11:46:21.528 - [任务 16][TWPOS_PS_INVC_TXN] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 11:46:21.528 - [任务 16][TWPOS_PS_INVC_TXN] - Use existing batch read offset: {"TWPOS_PS_INVC_TXN":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"_data":{"value":"826854D3AB000000022B022C0100296E5A100478F4BA54F0EE43FB9251741BD3B9A8C146645F69640064685115C048108A26BFC170A20004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.536 - [任务 16][TWPOS_PS_INVC] - Batch read completed. 
[TRACE] 2025-06-20 11:46:21.536 - [任务 16][TWPOS_PS_INVC] - Incremental sync starting... 
[TRACE] 2025-06-20 11:46:21.536 - [任务 16][TWPOS_PS_INVC] - Initial sync completed 
[TRACE] 2025-06-20 11:46:21.537 - [任务 16][TWPOS_PS_INVC] - Starting stream read, table list: [TWPOS_PS_INVC], offset: {"_data":{"value":"826854D358000000022B022C0100296E5A1004B9BC4098C3E340C19D111B664D1CDC1C46645F69640064685115C048108A26BFC170A10004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.537 - [任务 16][TWPOS_PS_INVC] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 11:46:21.592 - [任务 16][MDM_NEW_TWPOS] - Sink connector(MDM_NEW_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:46:21.592 - [任务 16][MDM_NEW_TWPOS] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-20 11:46:21.592 - [任务 16][MDM_NEW_TWPOS] - Apply table structure to target database 
[TRACE] 2025-06-20 11:46:21.635 - [任务 16][TWPOS_PS_INVC] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC], data change syncing 
[INFO ] 2025-06-20 11:46:21.635 - [任务 16][TWPOS_PS_INVC_TXN] - Batch read completed. 
[TRACE] 2025-06-20 11:46:21.635 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync starting... 
[TRACE] 2025-06-20 11:46:21.636 - [任务 16][TWPOS_PS_INVC_TXN] - Initial sync completed 
[TRACE] 2025-06-20 11:46:21.636 - [任务 16][TWPOS_PS_INVC_TXN] - Starting stream read, table list: [TWPOS_PS_INVC_TXN], offset: {"_data":{"value":"826854D3AB000000022B022C0100296E5A100478F4BA54F0EE43FB9251741BD3B9A8C146645F69640064685115C048108A26BFC170A20004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.636 - [任务 16][TWPOS_PS_INVC_TXN] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:46:21.675 - [任务 16][TWPOS_PS_INVC_TXN] - Connector MongoDB incremental start succeed, tables: [TWPOS_PS_INVC_TXN], data change syncing 
[INFO ] 2025-06-20 11:46:21.675 - [任务 16][MDM_TWPOS] - Source connector(MDM_TWPOS) initialization completed 
[TRACE] 2025-06-20 11:46:21.676 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" read batch size: 100 
[TRACE] 2025-06-20 11:46:21.676 - [任务 16][MDM_TWPOS] - Source node "MDM_TWPOS" event queue capacity: 200 
[INFO ] 2025-06-20 11:46:21.678 - [任务 16][MDM_TWPOS] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-20 11:46:21.679 - [任务 16][MDM_TWPOS] - Use existing batch read offset: {"MDM_TWPOS":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: {"_data":{"value":"826854D368000000022B022C0100296E5A10049C425F2A2138416DB70E62FD7473EBF146645F6964006468466FD436409D3D9426218C0004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.751 - [任务 16][MDM_TWPOS] - Batch read completed. 
[TRACE] 2025-06-20 11:46:21.751 - [任务 16][MDM_TWPOS] - Incremental sync starting... 
[TRACE] 2025-06-20 11:46:21.751 - [任务 16][MDM_TWPOS] - Initial sync completed 
[TRACE] 2025-06-20 11:46:21.751 - [任务 16][MDM_TWPOS] - Starting stream read, table list: [MDM_TWPOS], offset: {"_data":{"value":"826854D368000000022B022C0100296E5A10049C425F2A2138416DB70E62FD7473EBF146645F6964006468466FD436409D3D9426218C0004","bsonType":"STRING","number":false,"array":false,"null":false,"double":false,"boolean":false,"binary":false,"decimal128":false,"dbpointer":false,"timestamp":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"string":true,"int32":false,"int64":false,"objectId":false,"dateTime":false,"symbol":false}} 
[INFO ] 2025-06-20 11:46:21.751 - [任务 16][MDM_TWPOS] - Starting incremental sync using database log parser 
[TRACE] 2025-06-20 11:46:21.789 - [任务 16][MDM_TWPOS] - Connector MongoDB incremental start succeed, tables: [MDM_TWPOS], data change syncing 
[TRACE] 2025-06-20 11:46:23.601 - [任务 16][MDM_NEW_TWPOS] - The target node received dll event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@785747b2: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}). Wait for all previous events to be processed 
[TRACE] 2025-06-20 11:46:23.602 - [任务 16][MDM_NEW_TWPOS] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.index.TapCreateIndexEvent@785747b2: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.INVC_NBR"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"TWPOS_PS_INVC_TXN.TXN_ID"}]},{"indexFields":[{"fieldAsc":true,"name":"INVC_NBR"},{"fieldAsc":true,"name":"INVC_LINE_NBR"}]}],"tableId":"4e21e0a0-4471-48d9-a30f-ecf8078ab932","type":101}) 
[TRACE] 2025-06-20 11:46:26.253 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] running status set to false 
[TRACE] 2025-06-20 11:46:26.253 - [任务 16][TWPOS_PS_INVC] - PDK connector node stopped: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750391181367 
[TRACE] 2025-06-20 11:46:26.253 - [任务 16][TWPOS_PS_INVC] - PDK connector node released: HazelcastSourcePdkDataNode_51a10cba-3a5a-4dfd-9631-27f55d73882d_1750391181367 
[TRACE] 2025-06-20 11:46:26.253 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.254 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] monitor closed 
[TRACE] 2025-06-20 11:46:26.254 - [任务 16][TWPOS_PS_INVC] - Node TWPOS_PS_INVC[51a10cba-3a5a-4dfd-9631-27f55d73882d] close complete, cost 7 ms 
[TRACE] 2025-06-20 11:46:26.254 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] running status set to false 
[TRACE] 2025-06-20 11:46:26.254 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.254 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] monitor closed 
[TRACE] 2025-06-20 11:46:26.255 - [任务 16][JS_1] - Node JS_1[ef5593e5-b603-4c98-97e0-14f0eab89681] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:26.255 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] running status set to false 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node stopped: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750391181382 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][TWPOS_PS_INVC_TXN] - PDK connector node released: HazelcastSourcePdkDataNode_f0e184e1-55c7-4967-8c90-a748a579996a_1750391181382 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] monitor closed 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][TWPOS_PS_INVC_TXN] - Node TWPOS_PS_INVC_TXN[f0e184e1-55c7-4967-8c90-a748a579996a] close complete, cost 3 ms 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] running status set to false 
[TRACE] 2025-06-20 11:46:26.258 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.259 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] monitor closed 
[TRACE] 2025-06-20 11:46:26.259 - [任务 16][JS_2] - Node JS_2[a2ed499f-6c69-4e02-bfbc-f855705283f7] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:26.259 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] running status set to false 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][MDM_TWPOS] - PDK connector node stopped: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750391181378 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][MDM_TWPOS] - PDK connector node released: HazelcastSourcePdkDataNode_ed9c0b12-c8e2-4220-8fd7-e730ff199180_1750391181378 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] monitor closed 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][MDM_TWPOS] - Node MDM_TWPOS[ed9c0b12-c8e2-4220-8fd7-e730ff199180] close complete, cost 6 ms 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] running status set to false 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.265 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] monitor closed 
[TRACE] 2025-06-20 11:46:26.266 - [任务 16][JS_MAIN] - Node JS_MAIN[71e48ee7-176e-4214-9388-8b249fc284b3] close complete, cost 0 ms 
[TRACE] 2025-06-20 11:46:26.266 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] running status set to false 
[TRACE] 2025-06-20 11:46:26.266 - [任务 16][主从合并] - Destroy merge cache resource: -983984999 
[TRACE] 2025-06-20 11:46:26.266 - [任务 16][主从合并] - Destroy merge cache resource: 802081887 
[TRACE] 2025-06-20 11:46:26.268 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.268 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] monitor closed 
[TRACE] 2025-06-20 11:46:26.268 - [任务 16][主从合并] - Node 主从合并[4e21e0a0-4471-48d9-a30f-ecf8078ab932] close complete, cost 2 ms 
[TRACE] 2025-06-20 11:46:26.268 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] running status set to false 
[TRACE] 2025-06-20 11:46:26.272 - [任务 16][MDM_NEW_TWPOS] - PDK connector node stopped: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750391181382 
[TRACE] 2025-06-20 11:46:26.272 - [任务 16][MDM_NEW_TWPOS] - PDK connector node released: HazelcastTargetPdkDataNode_90141b1a-e809-4150-8fc4-0d0f7ae052ed_1750391181382 
[TRACE] 2025-06-20 11:46:26.272 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] schema data cleaned 
[TRACE] 2025-06-20 11:46:26.272 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] monitor closed 
[TRACE] 2025-06-20 11:46:26.478 - [任务 16][MDM_NEW_TWPOS] - Node MDM_NEW_TWPOS[90141b1a-e809-4150-8fc4-0d0f7ae052ed] close complete, cost 4 ms 
[TRACE] 2025-06-20 11:46:26.681 - [任务 16][TWPOS_PS_INVC] - Incremental sync completed 
[TRACE] 2025-06-20 11:46:26.816 - [任务 16][TWPOS_PS_INVC_TXN] - Incremental sync completed 
[TRACE] 2025-06-20 11:46:26.816 - [任务 16][MDM_TWPOS] - Incremental sync completed 
[TRACE] 2025-06-20 11:46:32.285 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 11:46:33.287 - [任务 16] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1fabf6fe 
[TRACE] 2025-06-20 11:46:33.287 - [任务 16] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5e96fcc5 
[TRACE] 2025-06-20 11:46:33.288 - [任务 16] - Stop task milestones: 68511c583717ee6ee2007d97(任务 16)  
[TRACE] 2025-06-20 11:46:33.405 - [任务 16] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:46:33.405 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 11:46:33.405 - [任务 16] - Task stopped. 
[TRACE] 2025-06-20 11:46:33.453 - [任务 16] - Remove memory task client succeed, task: 任务 16[68511c583717ee6ee2007d97] 
[TRACE] 2025-06-20 11:46:33.454 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[68511c583717ee6ee2007d97] 
