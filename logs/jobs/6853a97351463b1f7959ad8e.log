[TRACE] 2025-06-19 14:10:21.089 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:10:21.174 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:10:21.175 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:10:21.241 - [任务 19] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:10:21.241 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:10:21.265 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:10:21.265 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:10:21.471 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:10:21.824 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:10:21.860 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:10:21.860 - [任务 19][local_pg] - The table SampleTable has already exist. 
[INFO ] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:10:21.905 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:10:21.993 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:10:21.993 - [任务 19][sqlserver_ad] - building CT table for table SampleTable 
[INFO ] 2025-06-19 14:10:29.365 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.365 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:10:29.368 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:10:29.369 - [任务 19][sqlserver_ad] - Starting batch read from table: SampleTable 
[TRACE] 2025-06-19 14:10:29.447 - [任务 19][sqlserver_ad] - Table SampleTable is going to be initial synced 
[TRACE] 2025-06-19 14:10:29.447 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[INFO ] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Table SampleTable has been completed batch read 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:10:29.556 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:10:29.557 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SampleTable], offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:10:29.557 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:10:29.762 - [任务 19][sqlserver_ad] - opened cdc tables: [SampleTable] 
[WARN ] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=6853a9d50c793fc2478cc85d, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[04a3b729-701a-4cf5-93ec-d22f55ff7388], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Process after table "SampleTable" initial sync finished, cost: 17 ms 
[INFO ] 2025-06-19 14:10:29.897 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:10:30.384 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:10:30.384 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SampleTable], data change syncing 
[TRACE] 2025-06-19 14:10:47.102 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 14:10:47.305 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 14:10:50.111 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.111 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313421535 
[TRACE] 2025-06-19 14:10:50.112 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.112 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:10:50.113 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:10:50.113 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313421513 
[TRACE] 2025-06-19 14:10:50.121 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:10:50.122 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:10:50.122 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 9 ms 
[TRACE] 2025-06-19 14:10:58.878 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:10:59.884 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@38959f83 
[TRACE] 2025-06-19 14:10:59.887 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@405e29c3 
[TRACE] 2025-06-19 14:10:59.887 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 14:11:00.021 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:11:00.021 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:11:00.021 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 14:11:00.065 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:11:00.068 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:13:52.677 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:13:52.732 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:13:52.732 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:13:52.818 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:13:52.818 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:13:52.834 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:13:52.834 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:13:53.040 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:13:53.226 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:13:53.239 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:13:53.418 - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:13:53.419 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:13:53.546 - [任务 19][sqlserver_ad] - building CT table for table SourceOfRegion 
[INFO ] 2025-06-19 14:13:53.845 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:13:53.914 - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:13:54.086 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:13:54.087 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:13:54.097 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:13:54.097 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:13:54.109 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:13:54.109 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:13:54.118 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:13:54.118 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:13:54.127 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:13:54.127 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:13:54.138 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:13:54.138 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:13:54.339 - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"000000260000272B0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:13:54.524 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:13:54.703 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:13:54.913 - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:13:54.914 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:13:54.928 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:13:54.928 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 14:15:36.397 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 14:15:37.207 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750313633082 
[TRACE] 2025-06-19 14:15:39.406 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3010 ms 
[TRACE] 2025-06-19 14:15:39.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 14:15:39.420 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750313633095 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 14:15:39.421 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[TRACE] 2025-06-19 14:15:46.503 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 14:15:47.462 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@12e5e795 
[TRACE] 2025-06-19 14:15:47.462 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23e9bf10 
[TRACE] 2025-06-19 14:15:47.584 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 14:15:47.584 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 14:15:47.585 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 14:15:47.585 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 14:15:47.626 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:15:47.629 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 14:46:39.066 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 14:46:39.066 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 14:46:39.163 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 14:46:39.166 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 14:46:39.213 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 14:46:39.213 - [任务 19] - Task started 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 14:46:39.224 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 14:46:39.636 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 14:46:39.646 - [任务 19][local_pg] - Apply table structure to target database 
[TRACE] 2025-06-19 14:46:39.851 - [任务 19][local_pg] - The table SourceOfRegion has already exist. 
[INFO ] 2025-06-19 14:46:39.895 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 14:46:39.896 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 14:46:40.127 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Starting batch read from table: SourceOfRegion 
[TRACE] 2025-06-19 14:46:40.199 - [任务 19][sqlserver_ad] - Table SourceOfRegion is going to be initial synced 
[TRACE] 2025-06-19 14:46:40.278 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[TRACE] 2025-06-19 14:46:40.364 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 14:46:40.364 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-06-19 14:46:40.373 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-06-19 14:46:40.373 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-06-19 14:46:40.380 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-06-19 14:46:40.380 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-06-19 14:46:40.388 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-06-19 14:46:40.388 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-06-19 14:46:40.396 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-06-19 14:46:40.396 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-06-19 14:46:40.400 - [任务 19][local_pg] - Table 'SourceOfRegion' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-06-19 14:46:40.400 - [任务 19][local_pg] - Table 'SourceOfRegion' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Table SourceOfRegion has been completed batch read 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 14:46:40.764 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 14:46:40.765 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: {"currentStartLSN":"0000002700002AE20001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 14:46:40.765 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 14:46:40.965 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 14:46:41.165 - [任务 19][local_pg] - Process after table "SourceOfRegion" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 14:46:41.166 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 14:46:41.168 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:46:41.370 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[WARN ] 2025-06-19 14:48:09.148 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 14:48:09.234 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 14:48:09, next retry time 2025-06-19 14:49:09 
[INFO ] 2025-06-19 14:49:09.722 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 14:49:09.789 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 14:49:09.792 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 14:49:09.792 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.625000 
[TRACE] 2025-06-19 14:49:09.995 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 15:30:14.779 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 15:30:14.982 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750315599518 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:30:17.786 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750315599501 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:30:17.799 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:30:18.005 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[TRACE] 2025-06-19 15:30:25.105 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:30:25.993 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@65fd2f7c 
[TRACE] 2025-06-19 15:30:25.993 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@545248ab 
[TRACE] 2025-06-19 15:30:26.114 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 15:30:26.115 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:30:26.115 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:30:26.168 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 15:30:26.169 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:30:26.169 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:37:37.985 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 15:37:37.988 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 15:37:38.096 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 15:37:38.096 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:37:38.149 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:37:38.149 - [任务 19] - Task started 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:37:38.164 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:37:38.314 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 15:37:38.315 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:37:38.315 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 15:37:38.516 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 15:37:39.103 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 15:37:39.104 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"SourceOfRegion":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 15:37:39.164 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 15:37:39.165 - [任务 19][sqlserver_ad] - Starting stream read, table list: [SourceOfRegion], offset: "{\"currentStartLSN\":\"0000002700002C990004\",\"ddlOffset\":\"AAAAJwAAKuIAAQ==\",\"tablesOffset\":{\"SourceOfRegion\":\"0000002700002C990004\"}}" 
[INFO ] 2025-06-19 15:37:39.165 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:37:39.529 - [任务 19][sqlserver_ad] - opened cdc tables: [SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 15:37:39.933 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:37:39.933 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [SourceOfRegion], data change syncing 
[TRACE] 2025-06-19 15:41:17.904 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 15:41:18.722 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 15:41:20.907 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750318658183 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.908 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 15:41:20.909 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 15:41:20.909 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750318658183 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 15:41:20.927 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 15:41:21.133 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 18 ms 
[TRACE] 2025-06-19 15:41:26.620 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 15:41:27.598 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@59a9f549 
[TRACE] 2025-06-19 15:41:27.598 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b7fcc84 
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 15:41:27.718 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 15:41:27.779 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 15:41:27.779 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:41:27.779 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 15:43:51.285 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 15:43:51.332 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 15:43:51.332 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 15:43:51.393 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 15:43:51.393 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 15:43:51.407 - [任务 19] - Task started 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 15:43:51.407 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 15:43:51.797 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 15:43:51.798 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 15:43:51.798 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 15:43:52.003 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[TRACE] 2025-06-19 15:43:52.208 - [任务 19][sqlserver_ad] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-19 15:43:52.414 - [任务 19][sqlserver_ad] - building CT table for table dummy_test 
[INFO ] 2025-06-19 15:43:53.797 - [任务 19][sqlserver_ad] - Use existing stream offset: {"currentStartLSN":"00000028000029D40001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 15:43:53.797 - [任务 19][sqlserver_ad] - Starting batch read from 1 tables 
[TRACE] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Initial sync started 
[INFO ] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Starting batch read from table: dummy_test 
[TRACE] 2025-06-19 15:43:53.801 - [任务 19][sqlserver_ad] - Table dummy_test is going to be initial synced 
[TRACE] 2025-06-19 15:43:54.007 - [任务 19][sqlserver_ad] - Query snapshot row size completed: sqlserver_ad(04a3b729-701a-4cf5-93ec-d22f55ff7388) 
[INFO ] 2025-06-19 15:43:54.884 - [任务 19][sqlserver_ad] - Table dummy_test has been completed batch read 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Initial sync completed 
[INFO ] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: {"currentStartLSN":"00000028000029D40001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2025-06-19 15:43:54.885 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 15:43:55.296 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[TRACE] 2025-06-19 15:43:55.308 - [任务 19][local_pg] - Process after table "dummy_test" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-19 15:43:55.308 - [任务 19][local_pg] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-19 15:43:55.598 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:43:55.803 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2025-06-19 15:45:22.971 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 该连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.checkClosed(SQLServerConnection.java:1369)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.checkClosed(SQLServerStatement.java:1092)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.setFetchSize(SQLServerStatement.java:1754)
	com.zaxxer.hikari.pool.HikariProxyPreparedStatement.setFetchSize(HikariProxyPreparedStatement.java)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 15:45:22.971 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 15:45:22, next retry time 2025-06-19 15:46:22 
[INFO ] 2025-06-19 15:46:23.321 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 15:46:23.633 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.663000 
[TRACE] 2025-06-19 15:46:23.727 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 15:46:23.727 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 16:13:00.797 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 16:13:00.799 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750319031643 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750319031643 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 16:13:03.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 16:13:03.691 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3012 ms 
[TRACE] 2025-06-19 16:13:03.691 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 16:13:03.705 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750319031659 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750319031659 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 16:13:03.706 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 15 ms 
[TRACE] 2025-06-19 16:13:12.414 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:13:13.388 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@f764eea 
[TRACE] 2025-06-19 16:13:13.388 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@417dbfc3 
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:13:13.509 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:13:13.509 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 16:13:13.557 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:13:13.557 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:29:06.365 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 16:29:06.366 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 16:29:06.508 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 16:29:06.508 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 16:29:06.561 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 16:29:06.561 - [任务 19] - Task started 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 16:29:06.591 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 16:29:06.738 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 16:29:06.738 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 16:29:06.739 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 16:29:06.904 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 16:29:06.904 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 16:29:06.905 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000003500005C620004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003500005C620004\"}}" 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: "{\"currentStartLSN\":\"0000003500005C620004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003500005C620004\"}}" 
[INFO ] 2025-06-19 16:29:06.952 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 16:29:07.073 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 16:29:07.287 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 16:29:07.287 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 16:29:07.898 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 16:31:22.818 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 16:31:23.335 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321746608 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321746608 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 16:31:25.689 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 16:31:25.690 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 16:31:25.690 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321746612 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321746612 
[TRACE] 2025-06-19 16:31:25.703 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 16:31:25.704 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 16:31:25.909 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 14 ms 
[TRACE] 2025-06-19 16:31:34.842 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 16:31:35.651 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@32c7260f 
[TRACE] 2025-06-19 16:31:35.653 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4af4a9c5 
[TRACE] 2025-06-19 16:31:35.653 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 16:31:35.770 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 16:31:35.770 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 16:31:35.770 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 16:31:35.814 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:31:35.814 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 16:32:03.057 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 16:32:03.058 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 16:32:03.462 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 16:32:03.641 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 16:32:03.766 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 16:32:03.767 - [任务 19] - Task started 
[TRACE] 2025-06-19 16:32:03.825 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:32:03.825 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 16:32:03.825 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 16:32:03.825 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 16:32:04.584 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 16:32:04.585 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 16:32:04.585 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 16:32:04.590 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 16:32:04.590 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000003C0000041C0017\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003C0000041C0017\"}}" 
[INFO ] 2025-06-19 16:32:04.600 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 16:32:04.600 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 16:32:04.601 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 16:32:04.636 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 16:32:04.636 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 16:32:04.637 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 16:32:04.637 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 16:32:04.639 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: "{\"currentStartLSN\":\"0000003C0000041C0017\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000003C0000041C0017\"}}" 
[INFO ] 2025-06-19 16:32:04.639 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 16:32:04.843 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 16:32:05.081 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 16:32:05.082 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 16:32:10.575 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[WARN ] 2025-06-19 16:34:09.830 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 该连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDriverError(SQLServerException.java:237)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.checkClosed(SQLServerConnection.java:1369)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.checkClosed(SQLServerStatement.java:1092)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.getPreparedStatementHandle(SQLServerPreparedStatement.java:153)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.buildServerCursorPrepExecParams(SQLServerPreparedStatement.java:733)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 16:34:09.831 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 16:34:09, next retry time 2025-06-19 16:35:09 
[INFO ] 2025-06-19 16:35:10.078 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 16:35:10.250 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 16:35:10.254 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2025-06-19 16:35:10.254 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ success, total cost 00:01:00.479000 
[TRACE] 2025-06-19 16:35:10.382 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 16:35:10.383 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 17:00:50.845 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 17:00:51.548 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 17:00:53.855 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321923849 
[TRACE] 2025-06-19 17:00:53.855 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750321923849 
[TRACE] 2025-06-19 17:00:53.857 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 17:00:53.857 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 17:00:53.858 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3014 ms 
[TRACE] 2025-06-19 17:00:53.870 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 17:00:53.870 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321923849 
[TRACE] 2025-06-19 17:00:53.870 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750321923849 
[TRACE] 2025-06-19 17:00:53.870 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 17:00:53.871 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 17:00:53.871 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 12 ms 
[TRACE] 2025-06-19 17:00:59.773 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 17:01:00.752 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1bce50f6 
[TRACE] 2025-06-19 17:01:00.752 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5f3d95bb 
[TRACE] 2025-06-19 17:01:00.874 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 17:01:00.874 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 17:01:00.874 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 17:01:00.875 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 17:01:00.915 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 17:01:00.916 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 17:07:04.968 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 17:07:04.969 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 17:07:05.095 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 17:07:05.095 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 17:07:05.154 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 17:07:05.155 - [任务 19] - Task started 
[TRACE] 2025-06-19 17:07:05.178 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 17:07:05.178 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 17:07:05.178 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 17:07:05.333 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 17:07:05.333 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 17:07:05.333 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 17:07:05.333 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 17:07:05.538 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 17:07:05.591 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 17:07:05.591 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 17:07:05.592 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 17:07:05.592 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 17:07:05.593 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000004400005A0B0004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000004400005A0B0004\"}}" 
[INFO ] 2025-06-19 17:07:05.660 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 17:07:05.660 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 17:07:05.660 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 17:07:05.660 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: "{\"currentStartLSN\":\"0000004400005A0B0004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000004400005A0B0004\"}}" 
[INFO ] 2025-06-19 17:07:05.660 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 17:07:05.903 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 17:07:06.051 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 17:07:06.052 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 17:07:06.364 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[WARN ] 2025-06-19 17:07:47.602 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 未返回响应。连接已关闭。
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:613)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 17:07:47.602 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 1/10, first retry time 2025-06-19 17:07:47, next retry time 2025-06-19 17:08:47 
[WARN ] 2025-06-19 17:09:17.619 - [任务 19][sqlserver_ad] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 0, message: Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: 用户 'tapdata' 登录失败。 ClientConnectionId:61020513-f8dd-4730-aa01-a76cfd3e6b32
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.TDSTokenHandler.onEOF(tdsparser.java:283)
	com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:129)
	com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:37)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.sendLogon(SQLServerConnection.java:5560)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2025-06-19 17:09:17.620 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ, retry times 2/10, first retry time 2025-06-19 17:07:47, next retry time 2025-06-19 17:10:17 
[TRACE] 2025-06-19 17:09:26.124 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 17:09:29.714 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750324025194 
[TRACE] 2025-06-19 17:09:29.714 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750324025194 
[TRACE] 2025-06-19 17:09:29.715 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 17:09:29.715 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 17:09:29.717 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3592 ms 
[TRACE] 2025-06-19 17:09:29.718 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 17:09:29.729 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750324025194 
[TRACE] 2025-06-19 17:09:29.730 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750324025194 
[TRACE] 2025-06-19 17:09:29.730 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 17:09:29.730 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 17:09:29.935 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 13 ms 
[TRACE] 2025-06-19 17:09:37.647 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 17:09:38.655 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3748df92 
[TRACE] 2025-06-19 17:09:38.657 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40f83b6d 
[TRACE] 2025-06-19 17:09:38.657 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 17:09:38.775 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 17:09:38.776 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 17:09:38.776 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 17:09:38.819 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 17:09:38.820 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[INFO ] 2025-06-19 17:09:56.142 - [任务 19][sqlserver_ad] - Retry operation SOURCE_STREAM_READ failed, total cost 00:02:08.531000 
[TRACE] 2025-06-19 17:09:56.143 - [任务 19][sqlserver_ad] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null 
[ERROR] 2025-06-19 17:09:56.349 - [任务 19][sqlserver_ad] - java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null <-- Error Message -->
java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode$2.lambda$onChange$1(HazelcastPdkBaseNode.java:565)
	io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:80)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode$2.onChange(HazelcastPdkBaseNode.java:551)
	io.tapdata.pdk.core.utils.SampleRetryLifeCycle.exceededRetries(SampleRetryLifeCycle.java:73)
	io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:132)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:309)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initAndStartSourceRunner(HazelcastSourcePdkBaseNode.java:465)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:302)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:279)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	... 73 more
Caused by: java.lang.RuntimeException: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:82)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode$2.onChange(HazelcastPdkBaseNode.java:551)
	at io.tapdata.pdk.core.utils.SampleRetryLifeCycle.exceededRetries(SampleRetryLifeCycle.java:73)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:132)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 77 more
Caused by: java.lang.NullPointerException: Cannot invoke "io.tapdata.aspect.taskmilestones.RetryLifeCycleAspect.dataProcessorContext(com.tapdata.entity.task.context.DataProcessorContext)" because "aspect" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode$2.lambda$onChange$1(HazelcastPdkBaseNode.java:565)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:80)
	... 84 more

[TRACE] 2025-06-19 17:26:38.297 - [任务 19] - Task initialization... 
[TRACE] 2025-06-19 17:26:38.301 - [任务 19] - Start task milestones: 6853a97351463b1f7959ad8e(任务 19) 
[INFO ] 2025-06-19 17:26:38.459 - [任务 19] - Loading table structure completed 
[TRACE] 2025-06-19 17:26:38.459 - [任务 19] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-19 17:26:38.506 - [任务 19] - The engine receives 任务 19 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-19 17:26:38.506 - [任务 19] - Task started 
[TRACE] 2025-06-19 17:26:38.532 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] start preload schema,table counts: 1 
[TRACE] 2025-06-19 17:26:38.532 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] start preload schema,table counts: 1 
[TRACE] 2025-06-19 17:26:38.532 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] preload schema finished, cost 0 ms 
[TRACE] 2025-06-19 17:26:38.533 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] preload schema finished, cost 0 ms 
[INFO ] 2025-06-19 17:26:38.695 - [任务 19][local_pg] - Sink connector(local_pg) initialization completed 
[TRACE] 2025-06-19 17:26:38.695 - [任务 19][local_pg] - Node(local_pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-19 17:26:38.695 - [任务 19][local_pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-19 17:26:38.901 - [任务 19][local_pg] - Apply table structure to target database 
[INFO ] 2025-06-19 17:26:38.970 - [任务 19][sqlserver_ad] - Source connector(sqlserver_ad) initialization completed 
[TRACE] 2025-06-19 17:26:38.970 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" read batch size: 100 
[TRACE] 2025-06-19 17:26:38.971 - [任务 19][sqlserver_ad] - Source node "sqlserver_ad" event queue capacity: 200 
[INFO ] 2025-06-19 17:26:38.971 - [任务 19][sqlserver_ad] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2025-06-19 17:26:38.971 - [任务 19][sqlserver_ad] - Use existing batch read offset: {"dummy_test":{"batch_read_connector_status":"OVER"}}, Use existing stream offset: "{\"currentStartLSN\":\"0000004400005A0B0004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000004400005A0B0004\"}}" 
[INFO ] 2025-06-19 17:26:39.022 - [任务 19][sqlserver_ad] - Batch read completed. 
[TRACE] 2025-06-19 17:26:39.022 - [任务 19][sqlserver_ad] - Incremental sync starting... 
[TRACE] 2025-06-19 17:26:39.023 - [任务 19][sqlserver_ad] - Initial sync completed 
[TRACE] 2025-06-19 17:26:39.023 - [任务 19][sqlserver_ad] - Starting stream read, table list: [dummy_test], offset: "{\"currentStartLSN\":\"0000004400005A0B0004\",\"ddlOffset\":\"AAAAKAAAKdQAAQ==\",\"tablesOffset\":{\"dummy_test\":\"0000004400005A0B0004\"}}" 
[INFO ] 2025-06-19 17:26:39.226 - [任务 19][sqlserver_ad] - Starting incremental sync using database log parser 
[INFO ] 2025-06-19 17:26:39.331 - [任务 19][sqlserver_ad] - opened cdc tables: [dummy_test, SourceOfRegion, SampleTable] 
[INFO ] 2025-06-19 17:26:39.331 - [任务 19][sqlserver_ad] - Start to reading cdc table, fetch size: 500, interval: 500 ms 
[TRACE] 2025-06-19 17:26:39.331 - [任务 19][sqlserver_ad] - Connector SQL Server incremental start succeed, tables: [dummy_test], data change syncing 
[TRACE] 2025-06-19 17:26:39.725 - [任务 19][local_pg] - Table 'dummy_test' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-06-19 17:27:25.734 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] running status set to false 
[TRACE] 2025-06-19 17:27:25.937 - [任务 19][sqlserver_ad] - Incremental sync completed 
[TRACE] 2025-06-19 17:27:28.742 - [任务 19][sqlserver_ad] - PDK connector node stopped: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750325198558 
[TRACE] 2025-06-19 17:27:28.744 - [任务 19][sqlserver_ad] - PDK connector node released: HazelcastSourcePdkDataNode_04a3b729-701a-4cf5-93ec-d22f55ff7388_1750325198558 
[TRACE] 2025-06-19 17:27:28.744 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] schema data cleaned 
[TRACE] 2025-06-19 17:27:28.745 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] monitor closed 
[TRACE] 2025-06-19 17:27:28.745 - [任务 19][sqlserver_ad] - Node sqlserver_ad[04a3b729-701a-4cf5-93ec-d22f55ff7388] close complete, cost 3008 ms 
[TRACE] 2025-06-19 17:27:28.745 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] running status set to false 
[TRACE] 2025-06-19 17:27:28.753 - [任务 19][local_pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750325198554 
[TRACE] 2025-06-19 17:27:28.753 - [任务 19][local_pg] - PDK connector node released: HazelcastTargetPdkDataNode_db6ac1f3-d782-46d1-9065-615be84ff3a2_1750325198554 
[TRACE] 2025-06-19 17:27:28.753 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] schema data cleaned 
[TRACE] 2025-06-19 17:27:28.753 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] monitor closed 
[TRACE] 2025-06-19 17:27:28.958 - [任务 19][local_pg] - Node local_pg[db6ac1f3-d782-46d1-9065-615be84ff3a2] close complete, cost 10 ms 
[TRACE] 2025-06-19 17:28:01.468 - [任务 19] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-19 17:28:02.303 - [任务 19] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@25f6eb8e 
[TRACE] 2025-06-19 17:28:02.303 - [任务 19] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@34571f4c 
[TRACE] 2025-06-19 17:28:02.420 - [任务 19] - Stop task milestones: 6853a97351463b1f7959ad8e(任务 19)  
[TRACE] 2025-06-19 17:28:02.421 - [任务 19] - Stopped task aspect(s) 
[TRACE] 2025-06-19 17:28:02.421 - [任务 19] - Snapshot order controller have been removed 
[INFO ] 2025-06-19 17:28:02.421 - [任务 19] - Task stopped. 
[TRACE] 2025-06-19 17:28:02.464 - [任务 19] - Remove memory task client succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
[TRACE] 2025-06-19 17:28:02.464 - [任务 19] - Destroy memory task client cache succeed, task: 任务 19[6853a97351463b1f7959ad8e] 
