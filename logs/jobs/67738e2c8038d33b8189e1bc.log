[INFO ] 2024-12-31 14:25:58.044 - [任务 2] - Start task milestones: 67738e2c8038d33b8189e1bc(任务 2) 
[INFO ] 2024-12-31 14:25:58.044 - [任务 2] - Task initialization... 
[INFO ] 2024-12-31 14:25:58.177 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-31 14:25:58.225 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-31 14:25:58.225 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] start preload schema,table counts: 1 
[INFO ] 2024-12-31 14:25:58.225 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] start preload schema,table counts: 1 
[INFO ] 2024-12-31 14:25:58.226 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] preload schema finished, cost 0 ms 
[INFO ] 2024-12-31 14:25:58.226 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] preload schema finished, cost 0 ms 
[INFO ] 2024-12-31 14:25:58.738 - [任务 2][new_y] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-31 14:25:58.742 - [任务 2][FDM_subCollectionAccessoryModel] - Source node "FDM_subCollectionAccessoryModel" read batch size: 100 
[INFO ] 2024-12-31 14:25:58.742 - [任务 2][FDM_subCollectionAccessoryModel] - Source node "FDM_subCollectionAccessoryModel" event queue capacity: 200 
[INFO ] 2024-12-31 14:25:58.742 - [任务 2][FDM_subCollectionAccessoryModel] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-31 14:25:58.805 - [任务 2][FDM_subCollectionAccessoryModel] - batch offset found: {},stream offset found: {"cdcOffset":1735626357,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-31 14:25:58.805 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync started 
[INFO ] 2024-12-31 14:25:58.805 - [任务 2][FDM_subCollectionAccessoryModel] - Starting batch read, table name: FDM_subCollectionAccessoryModel 
[INFO ] 2024-12-31 14:25:58.805 - [任务 2][FDM_subCollectionAccessoryModel] - Table FDM_subCollectionAccessoryModel is going to be initial synced 
[INFO ] 2024-12-31 14:25:58.846 - [任务 2][FDM_subCollectionAccessoryModel] - Query snapshot row size completed: FDM_subCollectionAccessoryModel(1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4) 
[INFO ] 2024-12-31 14:25:58.846 - [任务 2][FDM_subCollectionAccessoryModel] - Table [FDM_subCollectionAccessoryModel] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-31 14:25:58.846 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync completed 
[INFO ] 2024-12-31 14:25:58.847 - [任务 2][FDM_subCollectionAccessoryModel] - Incremental sync starting... 
[INFO ] 2024-12-31 14:25:58.847 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync completed 
[INFO ] 2024-12-31 14:25:58.849 - [任务 2][FDM_subCollectionAccessoryModel] - Starting stream read, table list: [FDM_subCollectionAccessoryModel, _tapdata_heartbeat_table], offset: {"cdcOffset":1735626357,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-31 14:25:58.850 - [任务 2][FDM_subCollectionAccessoryModel] - Connector MongoDB incremental start succeed, tables: [FDM_subCollectionAccessoryModel, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-12-31 14:26:40.907 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] running status set to false 
[INFO ] 2024-12-31 14:26:40.915 - [任务 2][FDM_subCollectionAccessoryModel] - PDK connector node stopped: HazelcastSourcePdkDataNode_1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4_1735626358651 
[INFO ] 2024-12-31 14:26:40.915 - [任务 2][FDM_subCollectionAccessoryModel] - PDK connector node released: HazelcastSourcePdkDataNode_1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4_1735626358651 
[INFO ] 2024-12-31 14:26:40.916 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] schema data cleaned 
[INFO ] 2024-12-31 14:26:40.917 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] monitor closed 
[INFO ] 2024-12-31 14:26:40.918 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] close complete, cost 14 ms 
[INFO ] 2024-12-31 14:26:40.918 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] running status set to false 
[INFO ] 2024-12-31 14:26:40.934 - [任务 2][new_y] - Stop connector 
[INFO ] 2024-12-31 14:26:40.934 - [任务 2][new_y] - PDK connector node stopped: HazelcastTargetPdkDataNode_b4794f72-e2e3-4da4-8804-9c78ae7694c2_1735626358633 
[INFO ] 2024-12-31 14:26:40.934 - [任务 2][new_y] - PDK connector node released: HazelcastTargetPdkDataNode_b4794f72-e2e3-4da4-8804-9c78ae7694c2_1735626358633 
[INFO ] 2024-12-31 14:26:40.934 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] schema data cleaned 
[INFO ] 2024-12-31 14:26:40.934 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] monitor closed 
[INFO ] 2024-12-31 14:26:41.139 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] close complete, cost 16 ms 
[INFO ] 2024-12-31 14:26:41.341 - [任务 2][FDM_subCollectionAccessoryModel] - Incremental sync completed 
[INFO ] 2024-12-31 14:26:43.920 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-31 14:26:43.920 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5fe760fa 
[INFO ] 2024-12-31 14:26:44.039 - [任务 2] - Stop task milestones: 67738e2c8038d33b8189e1bc(任务 2)  
[INFO ] 2024-12-31 14:26:44.039 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-12-31 14:26:44.039 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-12-31 14:26:44.080 - [任务 2] - Remove memory task client succeed, task: 任务 2[67738e2c8038d33b8189e1bc] 
[INFO ] 2024-12-31 14:26:44.080 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67738e2c8038d33b8189e1bc] 
[INFO ] 2024-12-31 14:27:15.467 - [任务 2] - Start task milestones: 67738e2c8038d33b8189e1bc(任务 2) 
[INFO ] 2024-12-31 14:27:15.467 - [任务 2] - Task initialization... 
[INFO ] 2024-12-31 14:27:15.593 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-31 14:27:15.669 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-31 14:27:15.669 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] start preload schema,table counts: 1 
[INFO ] 2024-12-31 14:27:15.669 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] preload schema finished, cost 0 ms 
[INFO ] 2024-12-31 14:27:15.669 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] start preload schema,table counts: 1 
[INFO ] 2024-12-31 14:27:15.669 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] preload schema finished, cost 0 ms 
[INFO ] 2024-12-31 14:27:17.000 - [任务 2][FDM_subCollectionAccessoryModel] - Source node "FDM_subCollectionAccessoryModel" read batch size: 100 
[INFO ] 2024-12-31 14:27:17.000 - [任务 2][FDM_subCollectionAccessoryModel] - Source node "FDM_subCollectionAccessoryModel" event queue capacity: 200 
[INFO ] 2024-12-31 14:27:17.000 - [任务 2][FDM_subCollectionAccessoryModel] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-31 14:27:17.017 - [任务 2][new_y] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-31 14:27:17.073 - [任务 2][FDM_subCollectionAccessoryModel] - batch offset found: {},stream offset found: {"cdcOffset":1735626436,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-12-31 14:27:17.073 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync started 
[INFO ] 2024-12-31 14:27:17.073 - [任务 2][FDM_subCollectionAccessoryModel] - Starting batch read, table name: FDM_subCollectionAccessoryModel 
[INFO ] 2024-12-31 14:27:17.073 - [任务 2][FDM_subCollectionAccessoryModel] - Table FDM_subCollectionAccessoryModel is going to be initial synced 
[INFO ] 2024-12-31 14:27:17.077 - [任务 2][FDM_subCollectionAccessoryModel] - Query snapshot row size completed: FDM_subCollectionAccessoryModel(1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4) 
[INFO ] 2024-12-31 14:27:17.104 - [任务 2][FDM_subCollectionAccessoryModel] - Table [FDM_subCollectionAccessoryModel] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-31 14:27:17.104 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync completed 
[INFO ] 2024-12-31 14:27:17.104 - [任务 2][FDM_subCollectionAccessoryModel] - Incremental sync starting... 
[INFO ] 2024-12-31 14:27:17.104 - [任务 2][FDM_subCollectionAccessoryModel] - Initial sync completed 
[INFO ] 2024-12-31 14:27:17.133 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-12-31 14:27:17.133 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Step 1 - Check connection local_test2 enable share cdc: true 
[INFO ] 2024-12-31 14:27:17.133 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-12-31 14:27:17.144 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: CDC log cache task from local_test2 
[INFO ] 2024-12-31 14:27:17.144 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdata_css', table='TapExternalStorage', ttlDay=0] 
[INFO ] 2024-12-31 14:27:17.144 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Step 6 - Init read thread pool completed 
[INFO ] 2024-12-31 14:27:17.144 - [任务 2][FDM_subCollectionAccessoryModel] - Init share cdc reader completed 
[INFO ] 2024-12-31 14:27:17.144 - [任务 2][FDM_subCollectionAccessoryModel] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-12-31 14:27:17.145 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-12-31 14:27:17.145 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-12-31 14:27:17.152 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67738ec1b4e20b51c13a0b24, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67738e118038d33b8189e1ad_FDM_subCollectionAccessoryModel, version=v2, tableName=FDM_subCollectionAccessoryModel, externalStorageTableName=ExternalStorage_SHARE_CDC_-2044896044, shareCdcTaskId=67738eb08038d33b8189e2bd, connectionId=67738e118038d33b8189e1ad) 
[INFO ] 2024-12-31 14:27:17.152 - [任务 2][FDM_subCollectionAccessoryModel] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_CDC log cache task from local_test2_FDM_subCollectionAccessoryModel_任务 2, external storage name: ExternalStorage_SHARE_CDC_-2044896044 
[INFO ] 2024-12-31 14:27:17.154 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [FDM_subCollectionAccessoryModel] 
[INFO ] 2024-12-31 14:27:17.154 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Find sequence in construct(FDM_subCollectionAccessoryModel) by timestamp(2024-12-31T06:27:17Z): 1 
[INFO ] 2024-12-31 14:27:17.154 - [任务 2][FDM_subCollectionAccessoryModel] - Connector MongoDB incremental start succeed, tables: [FDM_subCollectionAccessoryModel], data change syncing 
[INFO ] 2024-12-31 14:27:17.154 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Starting read 'FDM_subCollectionAccessoryModel' log, sequence: 1 
[INFO ] 2024-12-31 14:27:17.359 - [任务 2][FDM_subCollectionAccessoryModel] - [Share CDC Task HZ Reader] - Find by FDM_subCollectionAccessoryModel filter: {sequence=1} 
[INFO ] 2024-12-31 14:29:28.344 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] running status set to false 
[INFO ] 2024-12-31 14:29:28.344 - [任务 2][FDM_subCollectionAccessoryModel] - Incremental sync completed 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][FDM_subCollectionAccessoryModel] - PDK connector node stopped: HazelcastSourcePdkDataNode_1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4_1735626435979 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][FDM_subCollectionAccessoryModel] - PDK connector node released: HazelcastSourcePdkDataNode_1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4_1735626435979 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] schema data cleaned 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] monitor closed 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][FDM_subCollectionAccessoryModel] - Node FDM_subCollectionAccessoryModel[1f36f0ac-6ff0-4470-ae5d-82c55a2f85b4] close complete, cost 14 ms 
[INFO ] 2024-12-31 14:29:28.353 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] running status set to false 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - Stop connector 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - PDK connector node stopped: HazelcastTargetPdkDataNode_b4794f72-e2e3-4da4-8804-9c78ae7694c2_1735626435966 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - PDK connector node released: HazelcastTargetPdkDataNode_b4794f72-e2e3-4da4-8804-9c78ae7694c2_1735626435966 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] schema data cleaned 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] monitor closed 
[INFO ] 2024-12-31 14:29:28.360 - [任务 2][new_y] - Node new_y[b4794f72-e2e3-4da4-8804-9c78ae7694c2] close complete, cost 6 ms 
[INFO ] 2024-12-31 14:29:29.405 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-31 14:29:29.406 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4040675b 
[INFO ] 2024-12-31 14:29:29.406 - [任务 2] - Stop task milestones: 67738e2c8038d33b8189e1bc(任务 2)  
[INFO ] 2024-12-31 14:29:29.521 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-12-31 14:29:29.521 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-12-31 14:29:29.552 - [任务 2] - Remove memory task client succeed, task: 任务 2[67738e2c8038d33b8189e1bc] 
[INFO ] 2024-12-31 14:29:29.552 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[67738e2c8038d33b8189e1bc] 
