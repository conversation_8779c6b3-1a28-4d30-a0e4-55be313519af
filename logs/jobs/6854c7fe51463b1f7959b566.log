[TRACE] 2025-06-20 10:31:44.940 - [任务 21] - Task initialization... 
[TRACE] 2025-06-20 10:31:45.005 - [任务 21] - Start task milestones: 6854c7fe51463b1f7959b566(任务 21) 
[INFO ] 2025-06-20 10:31:45.005 - [任务 21] - Loading table structure completed 
[TRACE] 2025-06-20 10:31:45.075 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-20 10:31:45.075 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 10:31:45.143 - [任务 21] - Task started 
[TRACE] 2025-06-20 10:31:45.143 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] start preload schema,table counts: 1 
[TRACE] 2025-06-20 10:31:45.143 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 10:31:45.143 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 10:31:45.143 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-20 10:31:45.526 - [任务 21][local_mongo] - Sink connector(local_mongo) initialization completed 
[TRACE] 2025-06-20 10:31:45.526 - [任务 21][local_mongo] - Node(local_mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-20 10:31:45.527 - [任务 21][local_mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-20 10:31:45.709 - [任务 21][local_mongo] - Apply table structure to target database 
[INFO ] 2025-06-20 10:31:45.709 - [任务 21][local_oracle] - Source connector(local_oracle) initialization completed 
[TRACE] 2025-06-20 10:31:45.709 - [任务 21][local_oracle] - Source node "local_oracle" read batch size: 100 
[TRACE] 2025-06-20 10:31:45.709 - [任务 21][local_oracle] - Source node "local_oracle" event queue capacity: 200 
[TRACE] 2025-06-20 10:31:45.709 - [任务 21][local_oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 10:31:45.914 - [任务 21][local_oracle] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":518819,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:31:45.936 - [任务 21][local_oracle] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 10:31:45.937 - [任务 21][local_oracle] - Initial sync started 
[INFO ] 2025-06-20 10:31:45.937 - [任务 21][local_oracle] - Starting batch read from table: IV_REGN 
[TRACE] 2025-06-20 10:31:45.937 - [任务 21][local_oracle] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Query snapshot row size completed: local_oracle(3020dc01-3b52-4f20-b9a8-1622ed51b65d) 
[INFO ] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Table IV_REGN has been completed batch read 
[TRACE] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Initial sync completed 
[INFO ] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Batch read completed. 
[TRACE] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Incremental sync starting... 
[TRACE] 2025-06-20 10:31:45.958 - [任务 21][local_oracle] - Initial sync completed 
[TRACE] 2025-06-20 10:31:45.959 - [任务 21][local_oracle] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":518819,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:31:45.959 - [任务 21][local_oracle] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 10:31:46.026 - [任务 21][local_oracle] - total start mining scn: 518819 
[TRACE] 2025-06-20 10:31:46.600 - [任务 21][local_mongo] - Process after table "IV_REGN" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 10:31:46.806 - [任务 21][local_mongo] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-20 10:31:47.414 - [任务 21][local_oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/XE/onlinelog/o1_mf_2_bxps4wxq_.log',options=>SYS.dbms_logmnr.NEW);END; 
[TRACE] 2025-06-20 10:32:28.401 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] running status set to false 
[INFO ] 2025-06-20 10:32:28.460 - [任务 21][local_oracle] - Log Miner is shutting down... 
[INFO ] 2025-06-20 10:32:28.461 - [任务 21][local_oracle] - Log Miner has been closed! 
[INFO ] 2025-06-20 10:32:28.461 - [任务 21][local_oracle] - Retry operation null failed, total cost 02:32:28.460000 
[TRACE] 2025-06-20 10:32:28.473 - [任务 21][local_oracle] - Incremental sync completed 
[TRACE] 2025-06-20 10:32:28.473 - [任务 21][local_oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1306): when operate table: unknown, Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[ERROR] 2025-06-20 10:32:28.507 - [任务 21][local_oracle] - Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initAndStartSourceRunner(HazelcastSourcePdkBaseNode.java:465)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:302)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:279)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518818 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 88 more

[TRACE] 2025-06-20 10:32:28.507 - [任务 21][local_oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750386705394 
[TRACE] 2025-06-20 10:32:28.507 - [任务 21][local_oracle] - PDK connector node released: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750386705394 
[TRACE] 2025-06-20 10:32:28.507 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] schema data cleaned 
[TRACE] 2025-06-20 10:32:28.508 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] monitor closed 
[TRACE] 2025-06-20 10:32:28.508 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] close complete, cost 171 ms 
[TRACE] 2025-06-20 10:32:28.508 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] running status set to false 
[TRACE] 2025-06-20 10:32:28.515 - [任务 21][local_mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750386705410 
[TRACE] 2025-06-20 10:32:28.515 - [任务 21][local_mongo] - PDK connector node released: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750386705410 
[TRACE] 2025-06-20 10:32:28.515 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] schema data cleaned 
[TRACE] 2025-06-20 10:32:28.515 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] monitor closed 
[TRACE] 2025-06-20 10:32:28.515 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] close complete, cost 7 ms 
[TRACE] 2025-06-20 10:32:35.808 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 10:32:36.729 - [任务 21] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@e72647c 
[TRACE] 2025-06-20 10:32:36.729 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@53fb0051 
[TRACE] 2025-06-20 10:32:36.854 - [任务 21] - Stop task milestones: 6854c7fe51463b1f7959b566(任务 21)  
[TRACE] 2025-06-20 10:32:36.854 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-06-20 10:32:36.855 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 10:32:36.855 - [任务 21] - Task stopped. 
[TRACE] 2025-06-20 10:32:36.901 - [任务 21] - Remove memory task client succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
[TRACE] 2025-06-20 10:32:36.903 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
[TRACE] 2025-06-20 10:32:43.326 - [任务 21] - Task initialization... 
[TRACE] 2025-06-20 10:32:43.330 - [任务 21] - Start task milestones: 6854c7fe51463b1f7959b566(任务 21) 
[INFO ] 2025-06-20 10:32:43.449 - [任务 21] - Loading table structure completed 
[TRACE] 2025-06-20 10:32:43.449 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-20 10:32:43.486 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 10:32:43.486 - [任务 21] - Task started 
[TRACE] 2025-06-20 10:32:43.498 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] start preload schema,table counts: 1 
[TRACE] 2025-06-20 10:32:43.498 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] start preload schema,table counts: 1 
[TRACE] 2025-06-20 10:32:43.498 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 10:32:43.498 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] preload schema finished, cost 0 ms 
[INFO ] 2025-06-20 10:32:43.909 - [任务 21][local_mongo] - Sink connector(local_mongo) initialization completed 
[TRACE] 2025-06-20 10:32:43.909 - [任务 21][local_mongo] - Node(local_mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-20 10:32:43.909 - [任务 21][local_mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-20 10:32:43.928 - [任务 21][local_mongo] - Apply table structure to target database 
[INFO ] 2025-06-20 10:32:44.136 - [任务 21][local_oracle] - Source connector(local_oracle) initialization completed 
[TRACE] 2025-06-20 10:32:44.136 - [任务 21][local_oracle] - Source node "local_oracle" read batch size: 100 
[TRACE] 2025-06-20 10:32:44.136 - [任务 21][local_oracle] - Source node "local_oracle" event queue capacity: 200 
[TRACE] 2025-06-20 10:32:44.136 - [任务 21][local_oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 10:32:44.314 - [任务 21][local_oracle] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":518839,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:32:44.314 - [任务 21][local_oracle] - Starting batch read from 1 tables 
[TRACE] 2025-06-20 10:32:44.317 - [任务 21][local_oracle] - Initial sync started 
[INFO ] 2025-06-20 10:32:44.317 - [任务 21][local_oracle] - Starting batch read from table: IV_REGN 
[TRACE] 2025-06-20 10:32:44.317 - [任务 21][local_oracle] - Table IV_REGN is going to be initial synced 
[TRACE] 2025-06-20 10:32:44.326 - [任务 21][local_oracle] - Query snapshot row size completed: local_oracle(3020dc01-3b52-4f20-b9a8-1622ed51b65d) 
[INFO ] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Table IV_REGN has been completed batch read 
[TRACE] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Initial sync completed 
[INFO ] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Batch read completed. 
[TRACE] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Incremental sync starting... 
[TRACE] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Initial sync completed 
[TRACE] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Starting stream read, table list: [IV_REGN], offset: {"sortString":null,"offsetValue":null,"lastScn":518839,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:32:44.335 - [任务 21][local_oracle] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 10:32:44.540 - [任务 21][local_oracle] - total start mining scn: 518839 
[TRACE] 2025-06-20 10:32:44.956 - [任务 21][local_mongo] - Process after table "IV_REGN" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 10:32:44.956 - [任务 21][local_mongo] - Process after all table(s) initial sync are finished，table number: 1 
[INFO ] 2025-06-20 10:32:45.525 - [任务 21][local_oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/XE/onlinelog/o1_mf_2_bxps4wxq_.log',options=>SYS.dbms_logmnr.NEW);END; 
[TRACE] 2025-06-20 10:35:50.647 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] running status set to false 
[INFO ] 2025-06-20 10:35:50.696 - [任务 21][local_oracle] - Log Miner is shutting down... 
[INFO ] 2025-06-20 10:35:50.696 - [任务 21][local_oracle] - Log Miner has been closed! 
[INFO ] 2025-06-20 10:35:50.696 - [任务 21][local_oracle] - Retry operation null failed, total cost 02:35:50.695000 
[TRACE] 2025-06-20 10:35:50.708 - [任务 21][local_oracle] - Incremental sync completed 
[TRACE] 2025-06-20 10:35:50.708 - [任务 21][local_oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 1306): when operate table: unknown, Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 
[ERROR] 2025-06-20 10:35:50.729 - [任务 21][local_oracle] - Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()
 <-- Error Message -->
Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()


<-- Simple Stack Trace -->
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	...

<-- Full Stack Trace -->
Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:109)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:904)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:926)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:916)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:290)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initAndStartSourceRunner(HazelcastSourcePdkBaseNode.java:465)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:302)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:279)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: Error : 1306, Position : 0, Sql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), OriginalSql = SELECT SCN,OPERATION,TIMESTAMP,STATUS,SQL_REDO,SQL_UNDO,ROW_ID,TABLE_NAME,RS_ID,SSN,(XIDUSN || '.' || XIDSLT || '.' || XIDSQN) AS XID, OPERATION_CODE, SEG_OWNER, CSF, ROLLBACK, THREAD#, COMMIT_TIMESTAMP, INFO, PXID, SEQUENCE# FROM V$LOGMNR_CONTENTS WHERE  SCN > 518947 AND (operation = 'COMMIT' OR operation = 'ROLLBACK' OR (SEG_TYPE IN (2, 19, 34)  AND SEG_OWNER='HDTEST' AND TABLE_NAME IN ('IV_REGN'))), Error Msg = ORA-01306: 在从 v$logmnr_contents 中选择之前必须调用 dbms_logmnr.start_logmnr()

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:637)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:564)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1151)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:771)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	... 88 more

[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750386763828 
[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_oracle] - PDK connector node released: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750386763828 
[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] schema data cleaned 
[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] monitor closed 
[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] close complete, cost 83 ms 
[TRACE] 2025-06-20 10:35:50.730 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] running status set to false 
[TRACE] 2025-06-20 10:35:50.739 - [任务 21][local_mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750386763799 
[TRACE] 2025-06-20 10:35:50.739 - [任务 21][local_mongo] - PDK connector node released: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750386763799 
[TRACE] 2025-06-20 10:35:50.739 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] schema data cleaned 
[TRACE] 2025-06-20 10:35:50.739 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] monitor closed 
[TRACE] 2025-06-20 10:35:50.944 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] close complete, cost 9 ms 
[TRACE] 2025-06-20 10:35:57.095 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 10:35:58.097 - [任务 21] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@75814620 
[TRACE] 2025-06-20 10:35:58.101 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@42c49c4b 
[TRACE] 2025-06-20 10:35:58.101 - [任务 21] - Stop task milestones: 6854c7fe51463b1f7959b566(任务 21)  
[TRACE] 2025-06-20 10:35:58.219 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-06-20 10:35:58.219 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 10:35:58.220 - [任务 21] - Task stopped. 
[TRACE] 2025-06-20 10:35:58.263 - [任务 21] - Remove memory task client succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
[TRACE] 2025-06-20 10:35:58.264 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
[TRACE] 2025-06-20 10:40:06.333 - [任务 21] - Task initialization... 
[TRACE] 2025-06-20 10:40:06.336 - [任务 21] - Start task milestones: 6854c7fe51463b1f7959b566(任务 21) 
[INFO ] 2025-06-20 10:40:06.438 - [任务 21] - Loading table structure completed 
[TRACE] 2025-06-20 10:40:06.438 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-20 10:40:06.471 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 10:40:06.471 - [任务 21] - Task started 
[TRACE] 2025-06-20 10:40:06.482 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] start preload schema,table counts: 2 
[TRACE] 2025-06-20 10:40:06.482 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 10:40:06.482 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] start preload schema,table counts: 2 
[TRACE] 2025-06-20 10:40:06.482 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] preload schema finished, cost 0 ms 
[INFO ] 2025-06-20 10:40:06.876 - [任务 21][local_mongo] - Sink connector(local_mongo) initialization completed 
[TRACE] 2025-06-20 10:40:06.876 - [任务 21][local_mongo] - Node(local_mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-20 10:40:06.876 - [任务 21][local_mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-20 10:40:07.038 - [任务 21][local_mongo] - Apply table structure to target database 
[INFO ] 2025-06-20 10:40:07.038 - [任务 21][local_oracle] - Source connector(local_oracle) initialization completed 
[TRACE] 2025-06-20 10:40:07.038 - [任务 21][local_oracle] - Source node "local_oracle" read batch size: 100 
[TRACE] 2025-06-20 10:40:07.038 - [任务 21][local_oracle] - Source node "local_oracle" event queue capacity: 200 
[TRACE] 2025-06-20 10:40:07.038 - [任务 21][local_oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-20 10:40:07.217 - [任务 21][local_oracle] - Use existing stream offset: {"sortString":null,"offsetValue":null,"lastScn":519186,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:40:07.217 - [任务 21][local_oracle] - Starting batch read from 2 tables 
[TRACE] 2025-06-20 10:40:07.220 - [任务 21][local_oracle] - Initial sync started 
[INFO ] 2025-06-20 10:40:07.220 - [任务 21][local_oracle] - Starting batch read from table: IV_REGN 
[TRACE] 2025-06-20 10:40:07.220 - [任务 21][local_oracle] - Table IV_REGN is going to be initial synced 
[INFO ] 2025-06-20 10:40:07.240 - [任务 21][local_oracle] - Table IV_REGN has been completed batch read 
[INFO ] 2025-06-20 10:40:07.240 - [任务 21][local_oracle] - Starting batch read from table: INVOICE_DATA 
[TRACE] 2025-06-20 10:40:07.240 - [任务 21][local_oracle] - Table INVOICE_DATA is going to be initial synced 
[TRACE] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Query snapshot row size completed: local_oracle(3020dc01-3b52-4f20-b9a8-1622ed51b65d) 
[INFO ] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Table INVOICE_DATA has been completed batch read 
[TRACE] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Initial sync completed 
[INFO ] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Batch read completed. 
[TRACE] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Incremental sync starting... 
[TRACE] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Initial sync completed 
[TRACE] 2025-06-20 10:40:07.279 - [任务 21][local_oracle] - Starting stream read, table list: [IV_REGN, INVOICE_DATA], offset: {"sortString":null,"offsetValue":null,"lastScn":519186,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0,"pendingHexScn":null,"pendingTimestamp":null} 
[INFO ] 2025-06-20 10:40:07.280 - [任务 21][local_oracle] - Starting incremental sync using database log parser 
[INFO ] 2025-06-20 10:40:07.486 - [任务 21][local_oracle] - total start mining scn: 519186 
[TRACE] 2025-06-20 10:40:07.934 - [任务 21][local_mongo] - Process after table "INVOICE_DATA" initial sync finished, cost: 0 ms 
[TRACE] 2025-06-20 10:40:07.934 - [任务 21][local_mongo] - Process after table "IV_REGN" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-20 10:40:07.934 - [任务 21][local_mongo] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-06-20 10:40:08.442 - [任务 21][local_oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/XE/onlinelog/o1_mf_2_bxps4wxq_.log',options=>SYS.dbms_logmnr.NEW);END; 
[TRACE] 2025-06-20 11:44:09.739 - [任务 21] - Task initialization... 
[TRACE] 2025-06-20 11:44:09.949 - [任务 21] - Start task milestones: 6854c7fe51463b1f7959b566(任务 21) 
[INFO ] 2025-06-20 11:44:10.356 - [任务 21] - Loading table structure completed 
[TRACE] 2025-06-20 11:44:11.029 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-20 11:44:11.029 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-20 11:44:11.155 - [任务 21] - Task started 
[TRACE] 2025-06-20 11:44:11.155 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] start preload schema,table counts: 2 
[TRACE] 2025-06-20 11:44:11.155 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] preload schema finished, cost 0 ms 
[TRACE] 2025-06-20 11:44:11.358 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] start preload schema,table counts: 2 
[TRACE] 2025-06-20 11:44:11.358 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] preload schema finished, cost 0 ms 
[ERROR] 2025-06-20 11:44:14.743 - [任务 21][local_oracle] - Source connector(local_oracle) initialization error: Failed to init pdk connector, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 17002): when operate table: unknown, java.net.SocketException: Connection reset <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: PDK retry exception (Server Error Code 17002): when operate table: unknown, java.net.SocketException: Connection reset
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:205)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:279)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:180)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.net.SocketException: Connection reset
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.oracle.OracleConnector.initConnection(OracleConnector.java:109)
	at io.tapdata.connector.oracle.OracleConnector.onStart(OracleConnector.java:122)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	... 65 more
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:416)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readNIOPacket(NIOPacket.java:418)
	at oracle.net.ns.NSProtocolNIO.negotiateConnection(NSProtocolNIO.java:167)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:350)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:2372)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:657)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1042)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:90)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:733)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:649)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 78 more

[TRACE] 2025-06-20 11:44:14.743 - [任务 21][local_oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: java.net.SocketException: Connection reset 
[ERROR] 2025-06-20 11:44:14.769 - [任务 21][local_oracle] - java.net.SocketException: Connection reset <-- Error Message -->
java.net.SocketException: Connection reset

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:416)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readNIOPacket(NIOPacket.java:418)
	...

<-- Full Stack Trace -->
java.net.SocketException: Connection reset
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:183)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.submitBlockingTasklets(TaskletExecutionService.java:177)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService.beginExecute(TaskletExecutionService.java:156)
	at com.hazelcast.jet.impl.execution.ExecutionContext.beginExecution(ExecutionContext.java:233)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution0(JobExecutionService.java:568)
	at com.hazelcast.jet.impl.JobExecutionService.beginExecution(JobExecutionService.java:563)
	at com.hazelcast.jet.impl.operation.StartExecutionOperation.doRun(StartExecutionOperation.java:50)
	at com.hazelcast.jet.impl.operation.AsyncOperation.run(AsyncOperation.java:55)
	at com.hazelcast.spi.impl.operationservice.Operation.call(Operation.java:190)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.call(OperationRunnerImpl.java:283)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:258)
	at com.hazelcast.spi.impl.operationservice.impl.OperationRunnerImpl.run(OperationRunnerImpl.java:219)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.run(OperationExecutorImpl.java:411)
	at com.hazelcast.spi.impl.operationexecutor.impl.OperationExecutorImpl.runOrExecute(OperationExecutorImpl.java:438)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvokeLocal(Invocation.java:601)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.doInvoke(Invocation.java:580)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke0(Invocation.java:541)
	at com.hazelcast.spi.impl.operationservice.impl.Invocation.invoke(Invocation.java:241)
	at com.hazelcast.spi.impl.operationservice.impl.InvocationBuilderImpl.invoke(InvocationBuilderImpl.java:61)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipant(MasterContext.java:294)
	at com.hazelcast.jet.impl.MasterContext.invokeOnParticipants(MasterContext.java:277)
	at com.hazelcast.jet.impl.MasterJobContext.invokeStartExecution(MasterJobContext.java:506)
	at com.hazelcast.jet.impl.MasterJobContext.lambda$onInitStepCompleted$7(MasterJobContext.java:473)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$54(JobCoordinationService.java:1306)
	at com.hazelcast.jet.impl.JobCoordinationService.lambda$submitToCoordinatorThread$55(JobCoordinationService.java:1327)
	at com.hazelcast.internal.util.executor.CompletableFutureTask.run(CompletableFutureTask.java:64)
	at com.hazelcast.internal.util.executor.CachedExecutorServiceDelegate$Worker.run(CachedExecutorServiceDelegate.java:217)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.executeRun(HazelcastManagedThread.java:76)
	at com.hazelcast.internal.util.executor.HazelcastManagedThread.run(HazelcastManagedThread.java:102)
Caused by: java.net.SocketException: Connection reset
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:52)
	at io.tapdata.connector.oracle.OracleConnector.initConnection(OracleConnector.java:109)
	at io.tapdata.connector.oracle.OracleConnector.onStart(OracleConnector.java:122)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:203)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:203)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$3(HazelcastSourcePdkBaseNode.java:286)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:65)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:279)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:180)
	... 53 more
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:416)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readNIOPacket(NIOPacket.java:418)
	at oracle.net.ns.NSProtocolNIO.negotiateConnection(NSProtocolNIO.java:167)
	at oracle.net.ns.NSProtocol.connect(NSProtocol.java:350)
	at oracle.jdbc.driver.T4CConnection.connect(T4CConnection.java:2372)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:657)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1042)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:90)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:733)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:649)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:49)
	... 78 more

[TRACE] 2025-06-20 11:44:14.771 - [任务 21][local_oracle] - Job suspend in error handle 
[TRACE] 2025-06-20 11:44:14.793 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] running status set to false 
[TRACE] 2025-06-20 11:44:14.793 - [任务 21][local_oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750391051214 
[TRACE] 2025-06-20 11:44:14.794 - [任务 21][local_oracle] - PDK connector node released: HazelcastSourcePdkDataNode_3020dc01-3b52-4f20-b9a8-1622ed51b65d_1750391051214 
[TRACE] 2025-06-20 11:44:14.795 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] schema data cleaned 
[TRACE] 2025-06-20 11:44:14.795 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] monitor closed 
[TRACE] 2025-06-20 11:44:15.001 - [任务 21][local_oracle] - Node local_oracle[3020dc01-3b52-4f20-b9a8-1622ed51b65d] close complete, cost 21 ms 
[INFO ] 2025-06-20 11:45:11.317 - [任务 21][local_mongo] - Sink connector(local_mongo) initialization completed 
[TRACE] 2025-06-20 11:45:11.318 - [任务 21][local_mongo] - Node(local_mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-20 11:45:11.318 - [任务 21][local_mongo] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-06-20 11:45:11.341 - [任务 21][local_mongo] - Apply table structure to target database 
[TRACE] 2025-06-20 11:45:11.347 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] running status set to false 
[TRACE] 2025-06-20 11:45:11.347 - [任务 21][local_mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750391051209 
[TRACE] 2025-06-20 11:45:11.348 - [任务 21][local_mongo] - PDK connector node released: HazelcastTargetPdkDataNode_eb2b6485-05b7-4d94-aefe-6a41354438dc_1750391051209 
[TRACE] 2025-06-20 11:45:11.348 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] schema data cleaned 
[TRACE] 2025-06-20 11:45:11.349 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] monitor closed 
[TRACE] 2025-06-20 11:45:11.349 - [任务 21][local_mongo] - Node local_mongo[eb2b6485-05b7-4d94-aefe-6a41354438dc] close complete, cost 5 ms 
[TRACE] 2025-06-20 11:45:17.536 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-20 11:45:18.545 - [任务 21] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@627c6821 
[TRACE] 2025-06-20 11:45:18.553 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@fb15097 
[TRACE] 2025-06-20 11:45:18.553 - [任务 21] - Stop task milestones: 6854c7fe51463b1f7959b566(任务 21)  
[TRACE] 2025-06-20 11:45:18.679 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-06-20 11:45:18.679 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-06-20 11:45:18.746 - [任务 21] - Task stopped. 
[TRACE] 2025-06-20 11:45:18.746 - [任务 21] - Remove memory task client succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
[TRACE] 2025-06-20 11:45:18.746 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[6854c7fe51463b1f7959b566] 
