[INFO ] 2024-12-09 18:16:12.048 - [R01NoProcessorTask] - Start task milestones: 6756c358f3dc0f3f05c5d8d8(R01NoProcessorTask) 
[INFO ] 2024-12-09 18:16:12.253 - [R01NoProcessorTask] - Task initialization... 
[INFO ] 2024-12-09 18:16:14.128 - [R01NoProcessorTask] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-12-09 18:16:14.339 - [R01NoProcessorTask] - The engine receives R01NoProcessorTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 18:16:14.394 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] start preload schema,table counts: 1 
[INFO ] 2024-12-09 18:16:14.394 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] start preload schema,table counts: 1 
[INFO ] 2024-12-09 18:16:14.395 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 18:16:14.600 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 18:16:15.167 - [R01NoProcessorTask][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-12-09 18:16:15.174 - [R01NoProcessorTask][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-12-09 18:16:15.174 - [R01NoProcessorTask][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-09 18:16:15.174 - [R01NoProcessorTask][AutoTestMongo] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-09 18:16:15.383 - [R01NoProcessorTask][AutoTestMySQL] - Node(AutoTestMySQL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-09 18:16:15.383 - [R01NoProcessorTask][AutoTestMySQL] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-12-09 18:16:15.432 - [R01NoProcessorTask][AutoTestMySQL] - Table "autoTest.R01NoProcessor" exists, skip auto create table 
[INFO ] 2024-12-09 18:16:15.433 - [R01NoProcessorTask][AutoTestMySQL] - The table R01NoProcessor has already exist. 
[INFO ] 2024-12-09 18:16:16.793 - [R01NoProcessorTask][AutoTestMongo] - Initial sync started 
[INFO ] 2024-12-09 18:16:16.793 - [R01NoProcessorTask][AutoTestMongo] - Starting batch read, table name: R01NoProcessor 
[INFO ] 2024-12-09 18:16:16.795 - [R01NoProcessorTask][AutoTestMongo] - Table R01NoProcessor is going to be initial synced 
[INFO ] 2024-12-09 18:16:16.830 - [R01NoProcessorTask][AutoTestMongo] - Query snapshot row size completed: AutoTestMongo(6ca43690-e9be-4344-ac0f-89915cec6164) 
[INFO ] 2024-12-09 18:16:16.831 - [R01NoProcessorTask][AutoTestMongo] - Table [R01NoProcessor] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-09 18:16:16.831 - [R01NoProcessorTask][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-12-09 18:16:20.519 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] running status set to false 
[INFO ] 2024-12-09 18:16:20.523 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] running status set to false 
[INFO ] 2024-12-09 18:16:20.568 - [R01NoProcessorTask][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_6ca43690-e9be-4344-ac0f-89915cec6164_1733739374888 
[INFO ] 2024-12-09 18:16:20.569 - [R01NoProcessorTask][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_6ca43690-e9be-4344-ac0f-89915cec6164_1733739374888 
[INFO ] 2024-12-09 18:16:20.569 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] schema data cleaned 
[INFO ] 2024-12-09 18:16:20.573 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] monitor closed 
[INFO ] 2024-12-09 18:16:20.573 - [R01NoProcessorTask][AutoTestMySQL] - PDK connector node stopped: HazelcastTargetPdkDataNode_10bef09f-c5ae-43fb-a0cf-a794b231cbdd_1733739374888 
[INFO ] 2024-12-09 18:16:20.573 - [R01NoProcessorTask][AutoTestMySQL] - PDK connector node released: HazelcastTargetPdkDataNode_10bef09f-c5ae-43fb-a0cf-a794b231cbdd_1733739374888 
[INFO ] 2024-12-09 18:16:20.576 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] schema data cleaned 
[INFO ] 2024-12-09 18:16:20.577 - [R01NoProcessorTask][AutoTestMongo] - Node AutoTestMongo[6ca43690-e9be-4344-ac0f-89915cec6164] close complete, cost 81 ms 
[INFO ] 2024-12-09 18:16:20.578 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] monitor closed 
[INFO ] 2024-12-09 18:16:20.786 - [R01NoProcessorTask][AutoTestMySQL] - Node AutoTestMySQL[10bef09f-c5ae-43fb-a0cf-a794b231cbdd] close complete, cost 81 ms 
[INFO ] 2024-12-09 18:16:22.941 - [R01NoProcessorTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 18:16:22.943 - [R01NoProcessorTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@488b0a7b 
[INFO ] 2024-12-09 18:16:23.124 - [R01NoProcessorTask] - Stop task milestones: 6756c358f3dc0f3f05c5d8d8(R01NoProcessorTask)  
[INFO ] 2024-12-09 18:16:23.124 - [R01NoProcessorTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 18:16:23.206 - [R01NoProcessorTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 18:16:23.207 - [R01NoProcessorTask] - Remove memory task client succeed, task: R01NoProcessorTask[6756c358f3dc0f3f05c5d8d8] 
[INFO ] 2024-12-09 18:16:23.414 - [R01NoProcessorTask] - Destroy memory task client cache succeed, task: R01NoProcessorTask[6756c358f3dc0f3f05c5d8d8] 
