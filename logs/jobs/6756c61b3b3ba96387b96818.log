[INFO ] 2024-12-09 18:28:08.266 - [R03<PERSON><PERSON>EditorTask] - Start task milestones: 6756c61b3b3ba96387b96818(R03FieldEditorTask) 
[INFO ] 2024-12-09 18:28:08.468 - [R03FieldEditorTask] - Task initialization... 
[INFO ] 2024-12-09 18:28:10.217 - [R03FieldEditorTask] - Node performs snapshot read asynchronously 
[INFO ] 2024-12-09 18:28:10.626 - [R03FieldEditorTask] - The engine receives R03FieldEditorTask task data from TM and will continue to run tasks by jet 
[INFO ] 2024-12-09 18:28:10.946 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] start preload schema,table counts: 1 
[INFO ] 2024-12-09 18:28:10.946 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 18:28:11.083 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] start preload schema,table counts: 1 
[INFO ] 2024-12-09 18:28:11.083 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] start preload schema,table counts: 1 
[INFO ] 2024-12-09 18:28:11.084 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 18:28:11.084 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] preload schema finished, cost 0 ms 
[INFO ] 2024-12-09 18:28:11.084 - [R03FieldEditorTask][Field Editor] - Node migrate_field_rename_processor(Field Editor: c2ce46db-522c-4008-bda3-11de22a14e90) enable batch process 
[INFO ] 2024-12-09 18:28:11.716 - [R03FieldEditorTask][AutoTestMongo] - Source node "AutoTestMongo" read batch size: 100 
[INFO ] 2024-12-09 18:28:11.716 - [R03FieldEditorTask][AutoTestMongo] - Source node "AutoTestMongo" event queue capacity: 200 
[INFO ] 2024-12-09 18:28:11.716 - [R03FieldEditorTask][AutoTestMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-12-09 18:28:11.716 - [R03FieldEditorTask][AutoTestMongo] - batch offset found: {},stream offset not found. 
[INFO ] 2024-12-09 18:28:12.107 - [R03FieldEditorTask][AutoTestMySQL] - Node(AutoTestMySQL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-12-09 18:28:12.108 - [R03FieldEditorTask][AutoTestMySQL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-12-09 18:28:12.981 - [R03FieldEditorTask][AutoTestMongo] - Initial sync started 
[INFO ] 2024-12-09 18:28:12.983 - [R03FieldEditorTask][AutoTestMongo] - Starting batch read, table name: R03FieldEditor 
[INFO ] 2024-12-09 18:28:12.983 - [R03FieldEditorTask][AutoTestMongo] - Table R03FieldEditor is going to be initial synced 
[INFO ] 2024-12-09 18:28:13.053 - [R03FieldEditorTask][AutoTestMongo] - Query snapshot row size completed: AutoTestMongo(3348b6db-edf8-4bd1-87c8-1200208e927c) 
[INFO ] 2024-12-09 18:28:13.258 - [R03FieldEditorTask][AutoTestMongo] - Table [R03FieldEditor] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-12-09 18:28:13.258 - [R03FieldEditorTask][AutoTestMongo] - Initial sync completed 
[INFO ] 2024-12-09 18:28:16.257 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] running status set to false 
[INFO ] 2024-12-09 18:28:16.257 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] running status set to false 
[INFO ] 2024-12-09 18:28:16.258 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] running status set to false 
[INFO ] 2024-12-09 18:28:16.258 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] schema data cleaned 
[INFO ] 2024-12-09 18:28:16.258 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] monitor closed 
[INFO ] 2024-12-09 18:28:16.258 - [R03FieldEditorTask][Field Editor] - Node Field Editor[c2ce46db-522c-4008-bda3-11de22a14e90] close complete, cost 3 ms 
[INFO ] 2024-12-09 18:28:16.280 - [R03FieldEditorTask][AutoTestMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode_3348b6db-edf8-4bd1-87c8-1200208e927c_1733740091417 
[INFO ] 2024-12-09 18:28:16.281 - [R03FieldEditorTask][AutoTestMongo] - PDK connector node released: HazelcastSourcePdkDataNode_3348b6db-edf8-4bd1-87c8-1200208e927c_1733740091417 
[INFO ] 2024-12-09 18:28:16.281 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] schema data cleaned 
[INFO ] 2024-12-09 18:28:16.282 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] monitor closed 
[INFO ] 2024-12-09 18:28:16.282 - [R03FieldEditorTask][AutoTestMongo] - Node AutoTestMongo[3348b6db-edf8-4bd1-87c8-1200208e927c] close complete, cost 34 ms 
[INFO ] 2024-12-09 18:28:16.339 - [R03FieldEditorTask][AutoTestMySQL] - PDK connector node stopped: HazelcastTargetPdkDataNode_1e95b415-b904-4b3a-bfdb-f09c66112788_1733740091237 
[INFO ] 2024-12-09 18:28:16.340 - [R03FieldEditorTask][AutoTestMySQL] - PDK connector node released: HazelcastTargetPdkDataNode_1e95b415-b904-4b3a-bfdb-f09c66112788_1733740091237 
[INFO ] 2024-12-09 18:28:16.340 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] schema data cleaned 
[INFO ] 2024-12-09 18:28:16.340 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] monitor closed 
[INFO ] 2024-12-09 18:28:16.352 - [R03FieldEditorTask][AutoTestMySQL] - Node AutoTestMySQL[1e95b415-b904-4b3a-bfdb-f09c66112788] close complete, cost 88 ms 
[INFO ] 2024-12-09 18:28:20.161 - [R03FieldEditorTask] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-12-09 18:28:20.175 - [R03FieldEditorTask] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@17f63657 
[INFO ] 2024-12-09 18:28:20.302 - [R03FieldEditorTask] - Stop task milestones: 6756c61b3b3ba96387b96818(R03FieldEditorTask)  
[INFO ] 2024-12-09 18:28:20.337 - [R03FieldEditorTask] - Stopped task aspect(s) 
[INFO ] 2024-12-09 18:28:20.337 - [R03FieldEditorTask] - Snapshot order controller have been removed 
[INFO ] 2024-12-09 18:28:20.452 - [R03FieldEditorTask] - Remove memory task client succeed, task: R03FieldEditorTask[6756c61b3b3ba96387b96818] 
[INFO ] 2024-12-09 18:28:20.452 - [R03FieldEditorTask] - Destroy memory task client cache succeed, task: R03FieldEditorTask[6756c61b3b3ba96387b96818] 
