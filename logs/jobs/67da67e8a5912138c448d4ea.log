[INFO ] 2025-03-19 14:45:23.322 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:45:23.376 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:45:23.376 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:45:23.376 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:45:23.376 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:45:23.394 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 17 ms 
[TRACE] 2025-03-19 14:45:23.394 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 18 ms 
[TRACE] 2025-03-19 14:45:23.580 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:45:23.686 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:45:23.686 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366723409 
[TRACE] 2025-03-19 14:45:23.687 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366723409 
[TRACE] 2025-03-19 14:45:23.687 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:45:23.687 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:45:23.698 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 2 ms 
[TRACE] 2025-03-19 14:45:23.698 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@33eec40e: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366723677,"type":300}
 
[ERROR] 2025-03-19 14:45:23.701 - [任务 27(101)][增强JS] - java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@33eec40e: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366723677,"type":300}
 <-- Error Message -->
java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@33eec40e: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366723677,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.base/java.util.Objects.requireNonNull(Objects.java:209)
	java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	java.base/java.nio.file.Path.of(Path.java:147)
	java.base/java.nio.file.Paths.get(Paths.java:69)
	com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:269)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:209)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	at java.base/java.nio.file.Path.of(Path.java:147)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	... 17 more

[TRACE] 2025-03-19 14:45:23.702 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:45:23.702 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:45:23.702 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:45:23.702 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:45:23.705 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 0 ms 
[TRACE] 2025-03-19 14:45:23.705 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] running status set to false 
[TRACE] 2025-03-19 14:45:23.705 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] schema data cleaned 
[TRACE] 2025-03-19 14:45:23.705 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] monitor closed 
[TRACE] 2025-03-19 14:45:23.705 - [任务 27(101)][51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] - Node 51a62fab-e5c4-43b3-be0a-5a3f1c40c07c[51a62fab-e5c4-43b3-be0a-5a3f1c40c07c] close complete, cost 0 ms 
[TRACE] 2025-03-19 14:45:23.712 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:45:23.712 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:45:23.712 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:45:23.712 - [任务 27(101)] - test run task 任务 27(101), cost 401ms 
[INFO ] 2025-03-19 14:46:54.358 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:46:54.400 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:46:54.400 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:46:54.400 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:46:54.400 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:46:54.410 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 9 ms 
[TRACE] 2025-03-19 14:46:54.410 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 9 ms 
[TRACE] 2025-03-19 14:46:54.615 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:46:54.711 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:46:54.711 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366814428 
[TRACE] 2025-03-19 14:46:54.712 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366814428 
[TRACE] 2025-03-19 14:46:54.712 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:46:54.712 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:46:54.712 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 4 ms 
[TRACE] 2025-03-19 14:46:54.731 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@c2cf023: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366814694,"type":300}
 
[ERROR] 2025-03-19 14:46:54.731 - [任务 27(101)][增强JS] - java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@c2cf023: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366814694,"type":300}
 <-- Error Message -->
java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@c2cf023: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366814694,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.base/java.util.Objects.requireNonNull(Objects.java:209)
	java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	java.base/java.nio.file.Path.of(Path.java:147)
	java.base/java.nio.file.Paths.get(Paths.java:69)
	com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:269)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:209)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	at java.base/java.nio.file.Path.of(Path.java:147)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	... 17 more

[TRACE] 2025-03-19 14:46:54.731 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:46:54.732 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:46:54.732 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:46:54.732 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:46:54.732 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 0 ms 
[TRACE] 2025-03-19 14:46:54.735 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] running status set to false 
[TRACE] 2025-03-19 14:46:54.735 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] schema data cleaned 
[TRACE] 2025-03-19 14:46:54.735 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] monitor closed 
[TRACE] 2025-03-19 14:46:54.737 - [任务 27(101)][952d15f9-050b-4b20-81cb-5720a0c36673] - Node 952d15f9-050b-4b20-81cb-5720a0c36673[952d15f9-050b-4b20-81cb-5720a0c36673] close complete, cost 0 ms 
[TRACE] 2025-03-19 14:46:54.738 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:46:54.738 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:46:54.738 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:46:54.939 - [任务 27(101)] - test run task 任务 27(101), cost 464ms 
[INFO ] 2025-03-19 14:48:13.498 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:48:13.629 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:48:13.633 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:48:13.633 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:48:13.633 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:48:13.658 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 28 ms 
[TRACE] 2025-03-19 14:48:13.661 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 27 ms 
[TRACE] 2025-03-19 14:48:13.664 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:48:14.201 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:48:14.216 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366893700 
[TRACE] 2025-03-19 14:48:14.217 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366893700 
[TRACE] 2025-03-19 14:48:14.217 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:48:14.217 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:48:22.293 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 17 ms 
[TRACE] 2025-03-19 14:48:25.799 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@34acdfb7: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366894185,"type":300}
 
[ERROR] 2025-03-19 14:48:25.800 - [任务 27(101)][增强JS] - java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@34acdfb7: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366894185,"type":300}
 <-- Error Message -->
java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@34acdfb7: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366894185,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.base/java.util.Objects.requireNonNull(Objects.java:209)
	java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	java.base/java.nio.file.Path.of(Path.java:147)
	java.base/java.nio.file.Paths.get(Paths.java:69)
	com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:269)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:209)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	at java.base/java.nio.file.Path.of(Path.java:147)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	... 18 more

[TRACE] 2025-03-19 14:48:25.802 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:48:25.802 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:48:25.803 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:48:25.803 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:48:25.804 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 2 ms 
[TRACE] 2025-03-19 14:48:25.806 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] running status set to false 
[TRACE] 2025-03-19 14:48:25.806 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] schema data cleaned 
[TRACE] 2025-03-19 14:48:25.806 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] monitor closed 
[TRACE] 2025-03-19 14:48:25.806 - [任务 27(101)][a38fb887-7e49-476a-aded-943015a22ee2] - Node a38fb887-7e49-476a-aded-943015a22ee2[a38fb887-7e49-476a-aded-943015a22ee2] close complete, cost 0 ms 
[TRACE] 2025-03-19 14:48:25.813 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:48:25.813 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:48:25.813 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:48:25.814 - [任务 27(101)] - test run task 任务 27(101), cost 12956ms 
[INFO ] 2025-03-19 14:48:29.302 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:48:29.402 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:48:29.402 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:48:29.402 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:48:29.403 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:48:29.419 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 16 ms 
[TRACE] 2025-03-19 14:48:29.422 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 20 ms 
[TRACE] 2025-03-19 14:48:29.423 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:48:29.721 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:48:29.730 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366909451 
[TRACE] 2025-03-19 14:48:29.730 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742366909451 
[TRACE] 2025-03-19 14:48:29.730 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:48:29.730 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:48:52.435 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 9 ms 
[TRACE] 2025-03-19 14:48:52.453 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@2b23d432: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366909715,"type":300}
 
[ERROR] 2025-03-19 14:48:52.454 - [任务 27(101)][增强JS] - java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@2b23d432: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366909715,"type":300}
 <-- Error Message -->
java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@2b23d432: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742366909715,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.base/java.util.Objects.requireNonNull(Objects.java:209)
	java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	java.base/java.nio.file.Path.of(Path.java:147)
	java.base/java.nio.file.Paths.get(Paths.java:69)
	com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:269)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:209)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	at java.base/java.nio.file.Path.of(Path.java:147)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:379)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	... 18 more

[TRACE] 2025-03-19 14:48:52.454 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:48:52.454 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:48:52.455 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:48:52.455 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:48:52.456 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 1 ms 
[TRACE] 2025-03-19 14:48:52.456 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] running status set to false 
[TRACE] 2025-03-19 14:48:52.456 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] schema data cleaned 
[TRACE] 2025-03-19 14:48:52.457 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] monitor closed 
[TRACE] 2025-03-19 14:48:52.457 - [任务 27(101)][67724def-f3dd-43dc-b97b-527b09ce4dee] - Node 67724def-f3dd-43dc-b97b-527b09ce4dee[67724def-f3dd-43dc-b97b-527b09ce4dee] close complete, cost 1 ms 
[TRACE] 2025-03-19 14:48:52.462 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:48:52.466 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:48:52.466 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:48:52.467 - [任务 27(101)] - test run task 任务 27(101), cost 23162ms 
[INFO ] 2025-03-19 14:58:45.404 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:58:45.531 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:58:45.534 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:58:45.535 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:58:45.536 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:58:45.561 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 26 ms 
[TRACE] 2025-03-19 14:58:45.562 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 22 ms 
[TRACE] 2025-03-19 14:58:45.763 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:58:46.094 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:58:46.103 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367525588 
[TRACE] 2025-03-19 14:58:46.104 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367525588 
[TRACE] 2025-03-19 14:58:46.104 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:58:46.104 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:58:46.110 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 11 ms 
[TRACE] 2025-03-19 14:58:46.141 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@6e6dc152: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742367526085,"type":300}
 
[ERROR] 2025-03-19 14:58:46.142 - [任务 27(101)][增强JS] - java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@6e6dc152: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742367526085,"type":300}
 <-- Error Message -->
java.lang.NullPointerExceptionio.tapdata.entity.event.dml.TapInsertRecordEvent@6e6dc152: {"after":{"department_id":1,"department_name":"Human Resources","location":"New York"},"containsIllegalDate":false,"tableId":"departments","time":1742367526085,"type":300}


<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.base/java.util.Objects.requireNonNull(Objects.java:209)
	java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	java.base/java.nio.file.Path.of(Path.java:147)
	java.base/java.nio.file.Paths.get(Paths.java:69)
	com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:381)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:269)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException
	at java.base/java.util.Objects.requireNonNull(Objects.java:209)
	at java.base/sun.nio.fs.UnixFileSystem.getPath(UnixFileSystem.java:263)
	at java.base/java.nio.file.Path.of(Path.java:147)
	at java.base/java.nio.file.Paths.get(Paths.java:69)
	at com.tapdata.processor.ScriptUtil.initBuildInMethod(ScriptUtil.java:381)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	... 18 more

[TRACE] 2025-03-19 14:58:46.144 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:58:46.144 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:58:46.145 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:58:46.146 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:58:46.146 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 2 ms 
[TRACE] 2025-03-19 14:58:46.150 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] running status set to false 
[TRACE] 2025-03-19 14:58:46.150 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] schema data cleaned 
[TRACE] 2025-03-19 14:58:46.150 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] monitor closed 
[TRACE] 2025-03-19 14:58:46.156 - [任务 27(101)][51950b2f-c3ea-42e4-bcd0-b95c95a84056] - Node 51950b2f-c3ea-42e4-bcd0-b95c95a84056[51950b2f-c3ea-42e4-bcd0-b95c95a84056] close complete, cost 1 ms 
[TRACE] 2025-03-19 14:58:46.156 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:58:46.156 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:58:46.156 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:58:46.362 - [任务 27(101)] - test run task 任务 27(101), cost 1370ms 
[INFO ] 2025-03-19 14:59:13.316 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 14:59:13.656 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:59:13.657 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] start preload schema,table counts: 0 
[TRACE] 2025-03-19 14:59:13.658 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 14:59:13.659 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 14:59:13.682 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 26 ms 
[TRACE] 2025-03-19 14:59:13.682 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 21 ms 
[TRACE] 2025-03-19 14:59:13.888 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 14:59:14.222 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 14:59:14.222 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367553714 
[TRACE] 2025-03-19 14:59:14.223 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367553714 
[TRACE] 2025-03-19 14:59:14.223 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 14:59:14.225 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 14:59:14.225 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 16 ms 
[TRACE] 2025-03-19 14:59:14.365 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist. 
[ERROR] 2025-03-19 14:59:14.365 - [任务 27(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist. <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	<js>.:program(<eval>:28)
	org.graalvm.polyglot.Context.eval(Context.java:402)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:162)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:527)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:492)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	at java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	... 25 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at <js>.:program(<eval>:28)
	at org.graalvm.polyglot.Context.eval(Context.java:402)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	... 29 more

[TRACE] 2025-03-19 14:59:14.367 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 14:59:14.368 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 14:59:14.369 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] running status set to false 
[TRACE] 2025-03-19 14:59:14.369 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] schema data cleaned 
[TRACE] 2025-03-19 14:59:14.369 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 14:59:14.369 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] monitor closed 
[TRACE] 2025-03-19 14:59:14.370 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 14:59:14.370 - [任务 27(101)][4c29d3a4-74a2-4252-b605-996d3d46b060] - Node 4c29d3a4-74a2-4252-b605-996d3d46b060[4c29d3a4-74a2-4252-b605-996d3d46b060] close complete, cost 1 ms 
[TRACE] 2025-03-19 14:59:14.370 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 2 ms 
[TRACE] 2025-03-19 14:59:14.376 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 14:59:14.376 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 14:59:14.376 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 14:59:14.377 - [任务 27(101)] - test run task 任务 27(101), cost 1489ms 
[INFO ] 2025-03-19 15:02:42.065 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 15:02:42.280 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] start preload schema,table counts: 0 
[TRACE] 2025-03-19 15:02:42.283 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:02:42.283 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:02:42.284 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 15:02:42.305 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 20 ms 
[TRACE] 2025-03-19 15:02:42.305 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 15:02:42.511 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 28 ms 
[TRACE] 2025-03-19 15:02:42.858 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 15:02:42.867 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367762334 
[TRACE] 2025-03-19 15:02:42.870 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742367762334 
[TRACE] 2025-03-19 15:02:42.871 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 15:02:42.873 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 15:02:42.873 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 15 ms 
[TRACE] 2025-03-19 15:02:43.042 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist. 
[ERROR] 2025-03-19 15:02:43.042 - [任务 27(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist. <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	<js>.:program(<eval>:28)
	org.graalvm.polyglot.Context.eval(Context.java:402)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:162)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:527)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:492)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	at java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	... 25 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class cn.hutool.http.HttpUtil is not allowed or does not exist.
	at <js>.:program(<eval>:28)
	at org.graalvm.polyglot.Context.eval(Context.java:402)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	... 29 more

[TRACE] 2025-03-19 15:02:43.045 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 15:02:43.045 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 15:02:43.046 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 15:02:43.047 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 15:02:43.047 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] running status set to false 
[TRACE] 2025-03-19 15:02:43.047 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 2 ms 
[TRACE] 2025-03-19 15:02:43.047 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] schema data cleaned 
[TRACE] 2025-03-19 15:02:43.048 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] monitor closed 
[TRACE] 2025-03-19 15:02:43.048 - [任务 27(101)][9927cd7e-06a8-4f1e-950b-3a71252c434d] - Node 9927cd7e-06a8-4f1e-950b-3a71252c434d[9927cd7e-06a8-4f1e-950b-3a71252c434d] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:02:43.053 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 15:02:43.053 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 15:02:43.054 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 15:02:43.054 - [任务 27(101)] - test run task 任务 27(101), cost 1428ms 
[INFO ] 2025-03-19 15:07:09.576 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 15:07:09.768 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:07:09.768 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:07:09.769 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] start preload schema,table counts: 0 
[TRACE] 2025-03-19 15:07:09.769 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 15:07:09.817 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 48 ms 
[TRACE] 2025-03-19 15:07:09.818 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 48 ms 
[TRACE] 2025-03-19 15:07:09.818 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 15:07:10.409 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 15:07:10.419 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368029844 
[TRACE] 2025-03-19 15:07:10.420 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368029844 
[TRACE] 2025-03-19 15:07:10.420 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 15:07:10.420 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 15:07:10.421 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 11 ms 
[TRACE] 2025-03-19 15:07:10.598 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist. 
[ERROR] 2025-03-19 15:07:10.598 - [任务 27(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist. <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist.

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist.
	<js>.:program(<eval>:2198)
	org.graalvm.polyglot.Context.eval(Context.java:402)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist.
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:162)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:527)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:492)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	at java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	... 24 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.example.TestUtil is not allowed or does not exist.
	at <js>.:program(<eval>:2198)
	at org.graalvm.polyglot.Context.eval(Context.java:402)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	... 28 more

[TRACE] 2025-03-19 15:07:10.600 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 15:07:10.601 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 15:07:10.602 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 15:07:10.602 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 15:07:10.604 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 1 ms 
[TRACE] 2025-03-19 15:07:10.604 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] running status set to false 
[TRACE] 2025-03-19 15:07:10.604 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] schema data cleaned 
[TRACE] 2025-03-19 15:07:10.604 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] monitor closed 
[TRACE] 2025-03-19 15:07:10.604 - [任务 27(101)][cff13762-ce91-4a48-9d40-6f3825be6c48] - Node cff13762-ce91-4a48-9d40-6f3825be6c48[cff13762-ce91-4a48-9d40-6f3825be6c48] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:07:10.612 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 15:07:10.612 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 15:07:10.612 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 15:07:10.613 - [任务 27(101)] - test run task 任务 27(101), cost 1473ms 
[INFO ] 2025-03-19 15:08:52.946 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 15:08:52.946 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] start preload schema,table counts: 0 
[TRACE] 2025-03-19 15:08:52.946 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:08:52.946 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 15:08:52.946 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:08:52.957 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 10 ms 
[TRACE] 2025-03-19 15:08:52.957 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 10 ms 
[TRACE] 2025-03-19 15:08:52.957 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 15:08:53.232 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 15:08:53.235 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368132977 
[TRACE] 2025-03-19 15:08:53.235 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368132977 
[TRACE] 2025-03-19 15:08:53.235 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 15:08:53.235 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 15:08:53.261 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 3 ms 
[TRACE] 2025-03-19 15:08:53.261 - [任务 27(101)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist. 
[ERROR] 2025-03-19 15:08:53.266 - [任务 27(101)][增强JS] - javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist. <-- Error Message -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist.

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist.
	<js>.:program(<eval>:2198)
	org.graalvm.polyglot.Context.eval(Context.java:402)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	...

<-- Full Stack Trace -->
javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist.
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:162)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getOrInitEngine(HazelcastJavaScriptProcessorNode.java:103)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.tryProcess(HazelcastJavaScriptProcessorNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$singleProcess$12(HazelcastProcessorBaseNode.java:299)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.singleProcess(HazelcastProcessorBaseNode.java:276)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:260)
	at com.hazelcast.jet.core.AbstractProcessor.tryProcess0(AbstractProcessor.java:187)
	at com.hazelcast.jet.core.AbstractProcessor.process0(AbstractProcessor.java:602)
	at com.hazelcast.jet.core.AbstractProcessor.process(AbstractProcessor.java:108)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$processInbox$2f647568$2(ProcessorTasklet.java:490)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.processInbox(ProcessorTasklet.java:490)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:341)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:336)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.stateMachineStep(ProcessorTasklet.java:328)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.call(ProcessorTasklet.java:291)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:315)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:527)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:492)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	at java.scripting/javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:262)
	at com.tapdata.processor.ScriptUtil.getScriptEngine(ScriptUtil.java:182)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.lambda$getOrInitEngine$2(HazelcastJavaScriptProcessorNode.java:151)
	... 24 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class org.tap.GeoParsing is not allowed or does not exist.
	at <js>.:program(<eval>:2198)
	at org.graalvm.polyglot.Context.eval(Context.java:402)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:490)
	... 28 more

[TRACE] 2025-03-19 15:08:53.266 - [任务 27(101)][增强JS] - Job suspend in error handle 
[TRACE] 2025-03-19 15:08:53.266 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[TRACE] 2025-03-19 15:08:53.266 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 15:08:53.266 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 15:08:53.267 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:08:53.267 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] running status set to false 
[TRACE] 2025-03-19 15:08:53.267 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] schema data cleaned 
[TRACE] 2025-03-19 15:08:53.267 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] monitor closed 
[TRACE] 2025-03-19 15:08:53.270 - [任务 27(101)][126c8ba2-b659-4d18-949e-64a4ebe84f59] - Node 126c8ba2-b659-4d18-949e-64a4ebe84f59[126c8ba2-b659-4d18-949e-64a4ebe84f59] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:08:53.270 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 15:08:53.270 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 15:08:53.270 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 15:08:53.476 - [任务 27(101)] - test run task 任务 27(101), cost 386ms 
[INFO ] 2025-03-19 15:09:15.236 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 15:09:15.317 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:09:15.317 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:09:15.317 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] start preload schema,table counts: 0 
[TRACE] 2025-03-19 15:09:15.332 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 15:09:15.332 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 14 ms 
[TRACE] 2025-03-19 15:09:15.334 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 16 ms 
[TRACE] 2025-03-19 15:09:15.334 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 15:09:15.602 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 15:09:15.604 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368155355 
[TRACE] 2025-03-19 15:09:15.605 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368155355 
[TRACE] 2025-03-19 15:09:15.605 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 15:09:15.605 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 15:09:15.605 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 3 ms 
[TRACE] 2025-03-19 15:09:16.202 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[INFO ] 2025-03-19 15:09:16.202 - [任务 27(101)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-pg_jerryTest-036181bb-2262-4be0-a64a-163a75b06635 
[INFO ] 2025-03-19 15:09:16.203 - [任务 27(101)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-pg_jerryTest-036181bb-2262-4be0-a64a-163a75b06635 
[INFO ] 2025-03-19 15:09:16.203 - [任务 27(101)][增强JS][src=user_script]  - [ScriptExecutorsManager-67da67e8a5912138c448d4ea-9572abaa-9285-45f2-b035-1be6f19720a4-67b7fcfbfa9aad768be1218c] schema data cleaned 
[TRACE] 2025-03-19 15:09:16.205 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] running status set to false 
[TRACE] 2025-03-19 15:09:16.205 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] schema data cleaned 
[TRACE] 2025-03-19 15:09:16.205 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 15:09:16.205 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] monitor closed 
[TRACE] 2025-03-19 15:09:16.205 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 15:09:16.206 - [任务 27(101)][bb45eeac-6322-4a60-8dac-fce4dc756266] - Node bb45eeac-6322-4a60-8dac-fce4dc756266[bb45eeac-6322-4a60-8dac-fce4dc756266] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:09:16.206 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 5 ms 
[TRACE] 2025-03-19 15:09:16.208 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 15:09:16.208 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 15:09:16.208 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 15:09:16.208 - [任务 27(101)] - test run task 任务 27(101), cost 975ms 
[INFO ] 2025-03-19 15:09:34.451 - [任务 27(101)] - 67da67e8a5912138c448d4ea task start 
[TRACE] 2025-03-19 15:09:34.515 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] start preload schema,table counts: 0 
[TRACE] 2025-03-19 15:09:34.515 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:09:34.516 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] start preload schema,table counts: 1 
[TRACE] 2025-03-19 15:09:34.516 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] preload schema finished, cost 0 ms 
[TRACE] 2025-03-19 15:09:34.526 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] preload schema finished, cost 10 ms 
[TRACE] 2025-03-19 15:09:34.526 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] preload schema finished, cost 10 ms 
[TRACE] 2025-03-19 15:09:34.728 - [任务 27(101)][增强JS] - Node js_processor(增强JS: 9572abaa-9285-45f2-b035-1be6f19720a4) enable batch process 
[TRACE] 2025-03-19 15:09:34.938 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] running status set to false 
[TRACE] 2025-03-19 15:09:34.938 - [任务 27(101)][departments] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368174542 
[TRACE] 2025-03-19 15:09:34.938 - [任务 27(101)][departments] - PDK connector node released: HazelcastSampleSourcePdkDataNode_7be61526-88af-4d44-ac76-1dc573e1678c_1742368174542 
[TRACE] 2025-03-19 15:09:34.938 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] schema data cleaned 
[TRACE] 2025-03-19 15:09:34.939 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] monitor closed 
[TRACE] 2025-03-19 15:09:34.939 - [任务 27(101)][departments] - Node departments[7be61526-88af-4d44-ac76-1dc573e1678c] close complete, cost 2 ms 
[TRACE] 2025-03-19 15:09:35.455 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] running status set to false 
[INFO ] 2025-03-19 15:09:35.458 - [任务 27(101)][增强JS][src=user_script]  - PDK connector node stopped: ScriptExecutor-pg_jerryTest-9d94ee1c-087a-49b7-8669-41c07fc0131c 
[INFO ] 2025-03-19 15:09:35.459 - [任务 27(101)][增强JS][src=user_script]  - PDK connector node released: ScriptExecutor-pg_jerryTest-9d94ee1c-087a-49b7-8669-41c07fc0131c 
[INFO ] 2025-03-19 15:09:35.460 - [任务 27(101)][增强JS][src=user_script]  - [ScriptExecutorsManager-67da67e8a5912138c448d4ea-9572abaa-9285-45f2-b035-1be6f19720a4-67b7fcfbfa9aad768be1218c] schema data cleaned 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] schema data cleaned 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] monitor closed 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][增强JS] - Node 增强JS[9572abaa-9285-45f2-b035-1be6f19720a4] close complete, cost 4 ms 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] running status set to false 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] schema data cleaned 
[TRACE] 2025-03-19 15:09:35.460 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] monitor closed 
[TRACE] 2025-03-19 15:09:35.462 - [任务 27(101)][d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] - Node d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7[d1dc1db1-d865-434f-8a5f-ce2f9e1bc0f7] close complete, cost 0 ms 
[TRACE] 2025-03-19 15:09:35.462 - [任务 27(101)] - Closed task monitor(s)
null 
[TRACE] 2025-03-19 15:09:35.462 - [任务 27(101)] - Closed task auto recovery instance
  null 
[TRACE] 2025-03-19 15:09:35.462 - [任务 27(101)] - Stopped task aspect(s) 
[INFO ] 2025-03-19 15:09:35.514 - [任务 27(101)] - test run task 任务 27(101), cost 1019ms 
