<?xml version='1.0' encoding='UTF-8'?>
<hudson>
  <disabledAdministrativeMonitors>
    <string>jenkins.security.QueueItemAuthenticatorMonitor</string>
    <string>hudson.diagnosis.ReverseProxySetupMonitor</string>
  </disabledAdministrativeMonitors>
  <version>2.319.1</version>
  <numExecutors>2</numExecutors>
  <mode>EXCLUSIVE</mode>
  <useSecurity>true</useSecurity>
  <authorizationStrategy class="com.michelin.cio.hudson.plugins.rolestrategy.RoleBasedAuthorizationStrategy">
    <roleMap type="globalRoles">
      <role name="admin" pattern=".*">
        <permissions>
          <permission>hudson.model.View.Delete</permission>
          <permission>hudson.model.Computer.Connect</permission>
          <permission>hudson.model.Run.Delete</permission>
          <permission>com.cloudbees.plugins.credentials.CredentialsProvider.ManageDomains</permission>
          <permission>hudson.model.Computer.Create</permission>
          <permission>hudson.model.View.Configure</permission>
          <permission>hudson.model.Computer.Build</permission>
          <permission>hudson.model.Item.Configure</permission>
          <permission>hudson.model.Hudson.Administer</permission>
          <permission>hudson.model.Item.Cancel</permission>
          <permission>hudson.model.Item.Read</permission>
          <permission>com.cloudbees.plugins.credentials.CredentialsProvider.View</permission>
          <permission>hudson.model.Computer.Delete</permission>
          <permission>hudson.model.Item.Build</permission>
          <permission>hudson.scm.SCM.Tag</permission>
          <permission>hudson.model.Item.Move</permission>
          <permission>hudson.model.Item.Discover</permission>
          <permission>hudson.model.Hudson.Read</permission>
          <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Update</permission>
          <permission>hudson.model.Item.Create</permission>
          <permission>hudson.model.Item.Workspace</permission>
          <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Delete</permission>
          <permission>hudson.model.Run.Replay</permission>
          <permission>hudson.model.View.Read</permission>
          <permission>hudson.model.View.Create</permission>
          <permission>hudson.model.Item.Delete</permission>
          <permission>hudson.model.Computer.Configure</permission>
          <permission>com.cloudbees.plugins.credentials.CredentialsProvider.Create</permission>
          <permission>hudson.model.Computer.Disconnect</permission>
          <permission>hudson.model.Run.Update</permission>
        </permissions>
        <assignedSIDs>
          <sid>admin</sid>
          <sid>license</sid>
          <sid>arthur</sid>
          <sid>license-temp</sid>
        </assignedSIDs>
      </role>
      <role name="kubesphere-user" pattern=".*">
        <permissions>
          <permission>hudson.model.Hudson.Read</permission>
        </permissions>
        <assignedSIDs>
          <sid>authenticated</sid>
        </assignedSIDs>
      </role>
    </roleMap>
    <roleMap type="slaveRoles"/>
    <roleMap type="projectRoles"/>
  </authorizationStrategy>
  <securityRealm class="hudson.security.LDAPSecurityRealm" plugin="ldap@1.26">
    <disableMailAddressResolver>false</disableMailAddressResolver>
    <configurations>
      <jenkins.security.plugins.ldap.LDAPConfiguration>
        <server>ldap://openldap.kubesphere-system.svc:389</server>
        <rootDN>dc=kubesphere,dc=io</rootDN>
        <inhibitInferRootDN>false</inhibitInferRootDN>
        <userSearchBase>ou=Users</userSearchBase>
        <userSearch>(&amp;(objectClass=inetOrgPerson)(|(uid={0})(mail={0})))</userSearch>
        <groupSearchBase>ou=Groups</groupSearchBase>
        <groupSearchFilter>(&amp;(objectClass=posixGroup)(cn={0}))</groupSearchFilter>
        <groupMembershipStrategy class="jenkins.security.plugins.ldap.FromGroupSearchLDAPGroupMembershipStrategy">
          <filter></filter>
        </groupMembershipStrategy>
        <managerDN>cn=admin,dc=kubesphere,dc=io</managerDN>
        <managerPasswordSecret>{AQAAABAAAAAQH/KnEa28ciMAE/IC90m7q5hREupzqdz0+wg53gtYQ0o=}</managerPasswordSecret>
        <displayNameAttributeName>uid</displayNameAttributeName>
        <mailAddressAttributeName>mail</mailAddressAttributeName>
        <ignoreIfUnavailable>false</ignoreIfUnavailable>
      </jenkins.security.plugins.ldap.LDAPConfiguration>
    </configurations>
    <disableRolePrefixing>true</disableRolePrefixing>
  </securityRealm>
  <disableRememberMe>true</disableRememberMe>
  <projectNamingStrategy class="jenkins.model.ProjectNamingStrategy$DefaultProjectNamingStrategy"/>
  <workspaceDir>${JENKINS_HOME}/workspace/${ITEM_FULLNAME}</workspaceDir>
  <buildsDir>${ITEM_ROOTDIR}/builds</buildsDir>
  <markupFormatter class="hudson.markup.EscapedMarkupFormatter"/>
  <jdks/>
  <viewsTabBar class="hudson.views.DefaultViewsTabBar"/>
  <myViewsTabBar class="hudson.views.DefaultMyViewsTabBar"/>
  <clouds>
    <org.csanchez.jenkins.plugins.kubernetes.KubernetesCloud plugin="kubernetes@1.31.2">
      <name>kubernetes</name>
      <defaultsProviderTemplate></defaultsProviderTemplate>
      <templates>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>448df781-c8e5-4387-b78d-32cf286c0f6e</id>
          <name>base</name>
          <namespace>kubesphere-devops-worker</namespace>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>base</label>
          <nodeUsageMode>NORMAL</nodeUsageMode>
          <hostNetwork>false</hostNetwork>
          <volumes>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/var/run/docker.sock</mountPath>
              <hostPath>/var/run/docker.sock</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.sonar/cache</mountPath>
              <hostPath>/var/data/jenkins_sonar_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
          </volumes>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>base</name>
              <image>kubesphere/builder-base:v3.2.0</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>cat</command>
              <args></args>
              <ttyEnabled>true</ttyEnabled>
              <resourceRequestCpu>100m</resourceRequestCpu>
              <resourceRequestMemory>100Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>4000m</resourceLimitCpu>
              <resourceLimitMemory>8192Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>jnlp</name>
              <image>ghcr.io/tapdata/v1build:0.7</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>jenkins-slave</command>
              <args>${computer.jnlpmac} ${computer.name}</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu>5000m</resourceRequestCpu>
              <resourceRequestMemory>8000Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>5000m</resourceLimitCpu>
              <resourceLimitMemory>8000Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars>
            <org.csanchez.jenkins.plugins.kubernetes.model.KeyValueEnvVar>
              <key>MAVEN_OPTS</key>
              <value>-Xmx7000m</value>
            </org.csanchez.jenkins.plugins.kubernetes.model.KeyValueEnvVar>
          </envVars>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yaml>spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: node-role.kubernetes.io/worker
            operator: In
            values:
            - ci
  tolerations:
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;NoSchedule&quot;
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;PreferNoSchedule&quot;
  containers:
  - name: &quot;base&quot;
    resources:
      requests:
        ephemeral-storage: &quot;1Gi&quot;
      limits:
        ephemeral-storage: &quot;10Gi&quot;
  securityContext:
    fsGroup: 1000
</yaml>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>61730e0e-0d49-4c80-96f4-e6317a93b90f</id>
          <name>nodejs</name>
          <namespace>kubesphere-devops-worker</namespace>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>nodejs</label>
          <hostNetwork>false</hostNetwork>
          <volumes>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/var/run/docker.sock</mountPath>
              <hostPath>/var/run/docker.sock</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.yarn</mountPath>
              <hostPath>/var/data/jenkins_nodejs_yarn_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.npm</mountPath>
              <hostPath>/var/data/jenkins_nodejs_npm_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.sonar/cache</mountPath>
              <hostPath>/var/data/jenkins_sonar_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
          </volumes>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>nodejs</name>
              <image>kubesphere/builder-nodejs:v3.2.0</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>cat</command>
              <args></args>
              <ttyEnabled>true</ttyEnabled>
              <resourceRequestCpu>100m</resourceRequestCpu>
              <resourceRequestMemory>100Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>4000m</resourceLimitCpu>
              <resourceLimitMemory>8192Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>jnlp</name>
              <image>jenkins/inbound-agent:4.10-2</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>${computer.jnlpmac} ${computer.name}</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu>50m</resourceRequestCpu>
              <resourceRequestMemory>400Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>500m</resourceLimitCpu>
              <resourceLimitMemory>1536Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yaml>spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: node-role.kubernetes.io/worker
            operator: In
            values:
            - ci
  tolerations:
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;NoSchedule&quot;
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;PreferNoSchedule&quot;
  containers:
  - name: &quot;nodejs&quot;
    resources:
      requests:
        ephemeral-storage: &quot;1Gi&quot;
      limits:
        ephemeral-storage: &quot;10Gi&quot;
  securityContext:
    fsGroup: 1000
</yaml>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>a1170719-da76-4972-a2e0-740d27fdae94</id>
          <name>maven</name>
          <namespace>kubesphere-devops-worker</namespace>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>maven</label>
          <hostNetwork>false</hostNetwork>
          <volumes>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/var/run/docker.sock</mountPath>
              <hostPath>/var/run/docker.sock</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.m2</mountPath>
              <hostPath>/var/data/jenkins_maven_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.sonar/cache</mountPath>
              <hostPath>/var/data/jenkins_sonar_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
          </volumes>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>maven</name>
              <image>kubesphere/builder-maven:v3.2.0</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>cat</command>
              <args></args>
              <ttyEnabled>true</ttyEnabled>
              <resourceRequestCpu>100m</resourceRequestCpu>
              <resourceRequestMemory>100Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>4000m</resourceLimitCpu>
              <resourceLimitMemory>8192Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>jnlp</name>
              <image>jenkins/inbound-agent:4.10-2</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>${computer.jnlpmac} ${computer.name}</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu>50m</resourceRequestCpu>
              <resourceRequestMemory>400Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>500m</resourceLimitCpu>
              <resourceLimitMemory>1536Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yaml>spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: node-role.kubernetes.io/worker
            operator: In
            values:
            - ci
  tolerations:
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;NoSchedule&quot;
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;PreferNoSchedule&quot;
  containers:
  - name: &quot;maven&quot;
    resources:
      requests:
        ephemeral-storage: &quot;1Gi&quot;
      limits:
        ephemeral-storage: &quot;10Gi&quot;
    volumeMounts:
    - name: config-volume
      mountPath: /opt/apache-maven-3.5.3/conf/settings.xml
      subPath: settings.xml
  volumes:
    - name: config-volume
      configMap:
        name: ks-devops-agent
        items:
        - key: MavenSetting
          path: settings.xml
  securityContext:
    fsGroup: 1000
</yaml>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>ff99efff-6417-4622-b13b-5b416ba226f9</id>
          <inheritFrom>maven</inheritFrom>
          <name>mavenjdk11</name>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>jdk11 maven java</label>
          <hostNetwork>false</hostNetwork>
          <volumes/>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>maven</name>
              <image>kubesphere/builder-maven:v3.2.0jdk11</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>9999999</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu></resourceRequestCpu>
              <resourceRequestMemory></resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu></resourceLimitCpu>
              <resourceLimitMemory></resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>e17e9b97-8383-45ed-9326-8106a9d3829a</id>
          <name>go</name>
          <namespace>kubesphere-devops-worker</namespace>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>go</label>
          <hostNetwork>false</hostNetwork>
          <volumes>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/var/run/docker.sock</mountPath>
              <hostPath>/var/run/docker.sock</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/home/<USER>/go/pkg</mountPath>
              <hostPath>/var/data/jenkins_go_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.sonar/cache</mountPath>
              <hostPath>/var/data/jenkins_sonar_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
          </volumes>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>go</name>
              <image>kubesphere/builder-go:v3.2.0</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>cat</command>
              <args></args>
              <ttyEnabled>true</ttyEnabled>
              <resourceRequestCpu>100m</resourceRequestCpu>
              <resourceRequestMemory>100Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>4000m</resourceLimitCpu>
              <resourceLimitMemory>8192Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>jnlp</name>
              <image>jenkins/inbound-agent:4.10-2</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>${computer.jnlpmac} ${computer.name}</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu>50m</resourceRequestCpu>
              <resourceRequestMemory>400Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>500m</resourceLimitCpu>
              <resourceLimitMemory>1536Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yaml>spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: node-role.kubernetes.io/worker
            operator: In
            values:
            - ci
  tolerations:
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;NoSchedule&quot;
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;PreferNoSchedule&quot;
  containers:
  - name: &quot;go&quot;
    resources:
      requests:
        ephemeral-storage: &quot;1Gi&quot;
      limits:
        ephemeral-storage: &quot;10Gi&quot;
  securityContext:
    fsGroup: 1000
</yaml>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>e7200b8b-ce90-4261-bada-fc7244941a68</id>
          <inheritFrom>go</inheritFrom>
          <name>go16</name>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>go16</label>
          <hostNetwork>false</hostNetwork>
          <volumes/>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>go</name>
              <image>kubesphere/builder-go:v3.2.0-1.16</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>9999999</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu></resourceRequestCpu>
              <resourceRequestMemory></resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu></resourceLimitCpu>
              <resourceLimitMemory></resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
        <org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
          <id>ab019e11-411a-4658-9bab-c66365afcbd3</id>
          <name>python</name>
          <namespace>kubesphere-devops-worker</namespace>
          <privileged>false</privileged>
          <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
          <alwaysPullImage>false</alwaysPullImage>
          <instanceCap>**********</instanceCap>
          <slaveConnectTimeout>1000</slaveConnectTimeout>
          <idleMinutes>0</idleMinutes>
          <activeDeadlineSeconds>0</activeDeadlineSeconds>
          <label>python</label>
          <hostNetwork>false</hostNetwork>
          <volumes>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/var/run/docker.sock</mountPath>
              <hostPath>/var/run/docker.sock</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.cache/pip</mountPath>
              <hostPath>/var/data/jenkins_python_pip_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.local/share/virtualenvs</mountPath>
              <hostPath>/var/data/jenkins_python_pipenv_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
            <org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
              <mountPath>/root/.sonar/cache</mountPath>
              <hostPath>/var/data/jenkins_sonar_cache</hostPath>
            </org.csanchez.jenkins.plugins.kubernetes.volumes.HostPathVolume>
          </volumes>
          <containers>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>python</name>
              <image>kubesphere/builder-python:v3.2.0</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>cat</command>
              <args></args>
              <ttyEnabled>true</ttyEnabled>
              <resourceRequestCpu>100m</resourceRequestCpu>
              <resourceRequestMemory>100Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>4000m</resourceLimitCpu>
              <resourceLimitMemory>8192Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
            <org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
              <name>jnlp</name>
              <image>jenkins/inbound-agent:4.10-2</image>
              <privileged>false</privileged>
              <alwaysPullImage>false</alwaysPullImage>
              <workingDir>/home/<USER>/agent</workingDir>
              <command>sleep</command>
              <args>${computer.jnlpmac} ${computer.name}</args>
              <ttyEnabled>false</ttyEnabled>
              <resourceRequestCpu>50m</resourceRequestCpu>
              <resourceRequestMemory>400Mi</resourceRequestMemory>
              <resourceRequestEphemeralStorage></resourceRequestEphemeralStorage>
              <resourceLimitCpu>500m</resourceLimitCpu>
              <resourceLimitMemory>1536Mi</resourceLimitMemory>
              <resourceLimitEphemeralStorage></resourceLimitEphemeralStorage>
              <envVars/>
              <ports/>
              <livenessProbe>
                <execArgs></execArgs>
                <timeoutSeconds>0</timeoutSeconds>
                <initialDelaySeconds>0</initialDelaySeconds>
                <failureThreshold>0</failureThreshold>
                <periodSeconds>0</periodSeconds>
                <successThreshold>0</successThreshold>
              </livenessProbe>
            </org.csanchez.jenkins.plugins.kubernetes.ContainerTemplate>
          </containers>
          <envVars/>
          <annotations/>
          <imagePullSecrets/>
          <nodeProperties/>
          <yaml>spec:
  affinity:
    nodeAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 1
        preference:
          matchExpressions:
          - key: node-role.kubernetes.io/worker
            operator: In
            values:
            - ci
  tolerations:
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;NoSchedule&quot;
  - key: &quot;node.kubernetes.io/ci&quot;
    operator: &quot;Exists&quot;
    effect: &quot;PreferNoSchedule&quot;
  containers:
  - name: &quot;python&quot;
    resources:
      requests:
        ephemeral-storage: &quot;1Gi&quot;
      limits:
        ephemeral-storage: &quot;10Gi&quot;
  securityContext:
    fsGroup: 1000
</yaml>
          <yamlMergeStrategy class="org.csanchez.jenkins.plugins.kubernetes.pod.yaml.Overrides"/>
          <showRawYaml>true</showRawYaml>
        </org.csanchez.jenkins.plugins.kubernetes.PodTemplate>
      </templates>
      <serverUrl>https://kubernetes.default</serverUrl>
      <useJenkinsProxy>false</useJenkinsProxy>
      <skipTlsVerify>true</skipTlsVerify>
      <addMasterProxyEnvVars>false</addMasterProxyEnvVars>
      <capOnlyOnAlivePods>false</capOnlyOnAlivePods>
      <namespace>kubesphere-devops-worker</namespace>
      <webSocket>false</webSocket>
      <directConnection>false</directConnection>
      <jenkinsUrl>http://devops-jenkins.kubesphere-devops-system:80</jenkinsUrl>
      <jenkinsTunnel>devops-jenkins-agent.kubesphere-devops-system:50000</jenkinsTunnel>
      <credentialsId>k8s-service-account</credentialsId>
      <containerCap>2</containerCap>
      <retentionTimeout>5</retentionTimeout>
      <connectTimeout>60</connectTimeout>
      <readTimeout>60</readTimeout>
      <podLabels>
        <org.csanchez.jenkins.plugins.kubernetes.PodLabel>
          <key>jenkins</key>
          <value>slave</value>
        </org.csanchez.jenkins.plugins.kubernetes.PodLabel>
      </podLabels>
      <usageRestricted>false</usageRestricted>
      <maxRequestsPerHost>32</maxRequestsPerHost>
      <waitForPodSec>600</waitForPodSec>
      <podRetention class="org.csanchez.jenkins.plugins.kubernetes.pod.retention.Never"/>
    </org.csanchez.jenkins.plugins.kubernetes.KubernetesCloud>
  </clouds>
  <quietPeriod>5</quietPeriod>
  <scmCheckoutRetryCount>2</scmCheckoutRetryCount>
  <views>
    <hudson.model.AllView>
      <owner class="hudson" reference="../../.."/>
      <name>all</name>
      <filterExecutors>false</filterExecutors>
      <filterQueue>false</filterQueue>
      <properties class="hudson.model.View$PropertyList"/>
    </hudson.model.AllView>
  </views>
  <primaryView>all</primaryView>
  <slaveAgentPort>50000</slaveAgentPort>
  <disabledAgentProtocols>
    <string>JNLP-connect</string>
    <string>JNLP2-connect</string>
  </disabledAgentProtocols>
  <label></label>
  <crumbIssuer class="hudson.security.csrf.DefaultCrumbIssuer">
    <excludeClientIPFromCrumb>true</excludeClientIPFromCrumb>
  </crumbIssuer>
  <nodeProperties/>
  <globalNodeProperties/>
  <noUsageStatistics>true</noUsageStatistics>
  <nodeRenameMigrationNeeded>true</nodeRenameMigrationNeeded>
</hudson>