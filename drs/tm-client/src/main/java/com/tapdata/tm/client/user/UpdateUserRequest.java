/**
 * @title: CreateUserLogsRequest
 * @description:
 * <AUTHOR>
 * @date 2021/6/29
 */
package com.tapdata.tm.client.user;

import com.tapdata.manager.common.http.MethodType;
import com.tapdata.manager.common.http.region.ProductDomain;
import com.tapdata.tm.client.BaseRequest;
import lombok.Getter;

@Getter
public class UpdateUserRequest extends BaseRequest<UpdateUserResponse> {

	private String username;

	private String nickname;

	private String photo;

	public UpdateUserRequest(ProductDomain productDomain, String userId) {
		super(productDomain, "/api/users/" + userId);
		this.setMethod(MethodType.PATCH);
	}

	@Override
	public Class<UpdateUserResponse> getResponseClass() {
		return UpdateUserResponse.class;
	}

	public void setUsername(String username) {
		this.username = username;
		this.putBodyParameter("username", username);
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
		this.putBodyParameter("nickname", nickname);
	}

	public void setPhoto(String photo) {
		this.photo = photo;
		this.putBodyParameter("photo", photo);
	}
}
