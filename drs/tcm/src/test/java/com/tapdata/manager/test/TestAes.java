package com.tapdata.manager.test;

import com.tapdata.manager.utils.SecurityUtil;
import org.junit.Assert;
import org.junit.Test;

/**
 * <AUTHOR>
 * create at 2022/10/25 下午5:43
 */
public class TestAes {

    @Test
    public void testAes() {

        String encodingText = SecurityUtil.encryption("test");
        String decodingText = SecurityUtil.decryption(encodingText);

        System.out.println(encodingText);
        System.out.println(decodingText);
        Assert.assertEquals("test", decodingText);

        String string = String.format("%s:%d",
                "6217294345f30fc23328b2b1", System.currentTimeMillis() / 60 / 1000);
        String md5Token = SecurityUtil.md5(string);
        System.out.println(string);
        System.out.println(md5Token);
    }
}
