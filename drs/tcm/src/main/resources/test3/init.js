db.drs_k8s_config.insertOne({
    "region" : "CIDC-RP-33",
    "zone" : "CIDC-RP-33-574",
    "enabled" : false,
    "defaultAgentNamespace" : "drs-agent",
    "defaultAgentVersion" : "0.0.13-test",
    "registry" : "",
    "defaultAgentPackage": "tapdata/drs-agent",
    "defaultNodeSelector": {
        "node-role.kubernetes.io/node": true
    },
    "nodeConfigs": [{
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }]
})

db.drs_k8s_config.insertOne({
    "region" : "CIDC-RP-25",
    "zone" : "CIDC-RP-25-57",
    "enabled" : false,
    "defaultAgentNamespace" : "drs-agent2557",
    "defaultAgentVersion" : "0.0.13-test",
    "registry" : "",
    "defaultAgentPackage": "tapdata/drs-agent",
    "defaultNodeSelector": {
        "node-role.kubernetes.io/node": true
    },
    "nodeConfigs": [{
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }]
})
db.drs_k8s_config.insertOne({
    "region" : "CIDC-RP-25",
    "zone" : "CIDC-RP-25-58",
    "enabled" : false,
    "defaultAgentNamespace" : "drs-agent2558",
    "defaultAgentVersion" : "0.0.13-test",
    "registry" : "",
    "defaultAgentPackage": "tapdata/drs-agent",
    "defaultNodeSelector": {
        "node-role.kubernetes.io/node": true
    },
    "nodeConfigs": [{
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }, {
        "ipv4": "*************"
    }]
})
