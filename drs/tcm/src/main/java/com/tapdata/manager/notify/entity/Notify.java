package com.tapdata.manager.notify.entity;

import com.tapdata.manager.base.entity.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2020/11/13 5:32 下午
 * @description
 */
@Getter
@Setter
@Document("mdb_notify")
public class Notify extends BaseEntity {

	private ObjectId clusterId;	// 集群ID
	private String notifyType;	// 通知类型
	private boolean read;		// 是否已读
	private String message;		// 通知消息
}
