package com.tapdata.manager.paid.repository;

import com.tapdata.manager.base.reporitory.BaseRepository;
import com.tapdata.manager.paid.entity.PaidCustomer;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;


@Repository
public class PaidCustomerRepository extends BaseRepository<PaidCustomer, ObjectId> {
    public PaidCustomerRepository(MongoTemplate mongoOperations) {
        super(PaidCustomer.class, mongoOperations);
    }
}
