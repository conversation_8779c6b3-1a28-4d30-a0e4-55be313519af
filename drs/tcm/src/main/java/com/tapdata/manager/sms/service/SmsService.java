package com.tapdata.manager.sms.service;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.HttpUtil;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.tapdata.manager.base.exception.BizException;
import com.tapdata.manager.common.utils.JsonUtil;
import com.tapdata.manager.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * create at 2022/2/15 上午9:51
 *
 * 短信服务
 */
@Service
@Slf4j
public class SmsService {

    @Value("${aliyun.sms.regionId:}")
    private String regionId;

    @Value("${aliyun.sms.accessKey:}")
    private String accessKey;

    @Value("${aliyun.sms.accessSecret:}")
    private String accessSecret;

    @Value("${aliyun.sms.domain:}")
    private String domain;

    @Value("${aliyun.sms.version:}")
    private String version;

    @Value("${aliyun.sms.signName:}")
    private String signName;

    public boolean send(String countryCode_, String phoneNumber, String templateCode, String templateParam) throws BizException {

        String countryCode = StringUtils.isNotBlank(countryCode_) ? countryCode_ : "86";
        if (countryCode.startsWith("+")) {
            countryCode = countryCode.replace("+", "");
        }

        if (log.isDebugEnabled()) {
            log.debug("Send sms message to {} {}, template code {}, templateParam {} ",
                    countryCode, phoneNumber, templateCode, templateParam);
            HttpUtil.setIsHttpDebug(true);
        }

        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKey, accessSecret);
        IAcsClient client = new DefaultAcsClient(profile);

        CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(domain);
        request.setSysVersion(version);
        request.setSysAction("SendSms");
        request.putQueryParameter("RegionId", regionId);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("PhoneNumbers", countryCode + phoneNumber);
        request.putQueryParameter("TemplateCode", templateCode);
        request.putQueryParameter("TemplateParam", templateParam);
        try {
            CommonResponse response = client.getCommonResponse(request);
            String json = response.getData();
            Map<String, Object> sendResult = JsonUtil.parseJson(json, Map.class);

            if (!"OK".equals(sendResult.get("Code"))) {
                log.error("Send sms failed {}", json);
                throw new BizException( sendResult.get("Code").toString(), sendResult.get("Message").toString() + "(" + sendResult.get("Code") + ")");
            }
            return true;

        } catch (ClientException e ) {
            log.error("Send sms error", e);
            throw new BizException("SMS.Send.Failed", e);
        }
    }

}
