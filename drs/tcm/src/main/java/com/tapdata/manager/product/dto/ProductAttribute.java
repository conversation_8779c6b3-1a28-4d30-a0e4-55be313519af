package com.tapdata.manager.product.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> @ gmail.com>
 * @date 2021/1/25 下午2:34
 * @description
 */
@Setter
@Getter
public class ProductAttribute {

    private String characterId;		//	可空	String		产品属性标识
    private String characterName;		//	可空	String		属性名
    private String characterValue;		//	可空	String		属性值
    private String characterGroup;		//	可空	String		属性组号
    private String canEmpty;		//	可空	String		是否可选
    private String canModify;		//	可空	String		是否可变更
}
