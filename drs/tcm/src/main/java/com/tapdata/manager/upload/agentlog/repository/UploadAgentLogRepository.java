package com.tapdata.manager.upload.agentlog.repository;

import com.tapdata.manager.base.reporitory.BaseRepository;
import com.tapdata.manager.upload.agentlog.entity.UploadAgentLog;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Repository;

@Repository
public class UploadAgentLogRepository extends BaseRepository<UploadAgentLog, ObjectId> {
    public UploadAgentLogRepository( MongoTemplate mongoOperations) {
        super(UploadAgentLog.class, mongoOperations);
    }
}
