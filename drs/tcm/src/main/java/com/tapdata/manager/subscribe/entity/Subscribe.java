package com.tapdata.manager.subscribe.entity;

import com.tapdata.manager.agent.dto.Spec;
import com.tapdata.manager.aliyun.market.dto.AssignLog;
import com.tapdata.manager.base.entity.BaseEntity;
import com.tapdata.manager.paid.dto.CancellationDetail;
import com.tapdata.manager.subscribe.UsageType;
import com.tapdata.manager.subscribe.dto.SubscribeItem;
import com.tapdata.manager.paid.dto.StripePay;
import com.tapdata.manager.subscribe.dto.SubscribeItemReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@EqualsAndHashCode(callSuper = true)
@Document("paid_subscribe")
@Data
public class Subscribe extends BaseEntity {

    // 支付成功、未支付、支付失败
    private String status;
    @Deprecated
    private Spec spec;
    // 单位精度为分
    @Deprecated
    private BigDecimal price;
    // 支付周期
    private String periodUnit; // MONTH, YEAR
    // 一次性或者订阅模式
    @Deprecated
    private String type;

    // 币种 美元、欧元、人民币
    private String currency;

    @Deprecated
    // 价格ID
    private String priceId;

    @Deprecated
    // 产品ID
    private String ProductId;

    // 数量
    private Integer quantity;
    @Deprecated
    private BigDecimal refundAmount;

    @Deprecated
    private Long periodStart;
    @Deprecated
    private Long periodEnd;

    private int period;
    @Deprecated
    private String agentId;
    @Deprecated
    private String payChannel;
    @Deprecated
    private StripePay stripePay;
    @Deprecated
    private BigDecimal discountAmount;
    @Deprecated
    private BigDecimal actualAmount;
    @Deprecated
    private String refundReason;
    @Deprecated
    private String refundDescribe;

    private String email;

    private String paymentType;

    private String platform;

    private String agentType;
    @Deprecated
    private String mongodbUrl;

    private String cloudProvider;

    // 支付渠道
    private String paymentMethod;

    // 优惠吗
    private String promotionCode;

    // 开始时间
    private Long startAt;

    // 结束时间
    private Long endAt;

    //
    private Long cancelAt;

    // 是否当期结束后取消订阅
    private boolean cancelAtPeriodEnd;

    // 是否当期结束后取消时间
    private Long canceledAt;
    // 退订意见反馈
    private List<CancellationDetail> cancellationDetails;

    // 订阅描述
    private String description;

    // 订阅内容
    private List<SubscribeItemReq> subscribeItems;

    private String subscribeType;
    private UsageType usageType; // 计费方式，subscribeType = recurring 时有效

    private long totalAmount;

    private Map<String, String> metadata;


}
