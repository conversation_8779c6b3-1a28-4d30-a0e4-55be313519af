package com.tapdata.manager.paid.dto;

import lombok.Data;

import java.util.List;

@Data
public class StripePay {

    private String sessionId;

    // 价格ID
    private String priceId;

    // 产品ID
    private String ProductId;

    private String chargeId;

    private String subscribeId;

    private String  invoiceId;

    private List<PaidRenewDetail> paidRenewDetail;

    private String payUrl;

    private String mdbPriceId;

    private String paymentMethodId;
    private String customerId;

}
