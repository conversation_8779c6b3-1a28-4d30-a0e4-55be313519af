name: OP 版本环境测一测

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

on:
  workflow_dispatch:
    inputs:
      ADDR:
        description: '环境地址'
        required: true
        type: string
      FEISHU_CHAT_ID:
        description: '结果通知人'
        required: true
        default: 'oc_79ef2aafcf9a712bfc31280f80498732'
        type: string
      SMART_CDC:
        description: "是否开启智能 CDC"
        required: true
        default: false
        type: boolean
      CORE:
        description: "是否只测试核心数据库"
        required: true
        default: true
        type: boolean

env:
  REGISTRY: ghcr.io
  ADDR: ${{ inputs.ADDR }}
  FEISHU_PERSON_IN_CHARGE: ${{ secrets.FEISHU_PERSON_IN_CHARGE }}
  FEISHU_CHAT_ID: ${{ inputs.FEISHU_CHAT_ID }}
  FEISHU_APP_ID: ${{ secrets.FEISHU_APP_ID}}
  FEISHU_APP_SECRET: ${{ secrets.FEISHU_APP_SECRET }}
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  SMART_CDC: ${{ inputs.SMART_CDC }}
  CORE: ${{ inputs.CORE }}

jobs:

  Build_And_Test:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - name: 获取测试代码分支
        run: |
          current_branch="develop-v2.12"
          echo "OPENSOURCE_BRANCH=$current_branch" >> $GITHUB_ENV
      - name: 下载测试代码
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          repository: tapdata/tapdata
          ref: ${{ env.OPENSOURCE_BRANCH }}
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
      - name: "P0 用例集成测试"
        env:
          CASE_CONFIG: ${{ secrets.CASE_CONFIG }}
        run: |
          sudo apt update -y
          sudo apt install -y libmysqlclient-dev
          pip3 install poetry
          cd tapshell && pip3 install . && cd ..
          cd auto-test && pip3 install -r requirements.txt && cd -
          cd auto-test && chmod +x ./test_env.sh && bash ./test_env.sh && cd -
