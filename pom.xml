<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.tapdata</groupId>
    <artifactId>enterprise-parent</artifactId>
    <version>0.0.1</version>
    <packaging>pom</packaging>
    <name>Enterprise Parent</name>
    <modules>
        <module>tm-enterprise</module>
        <module>iengine-enterprise</module>
    </modules>

    <properties>
        <sonar.organization>tapdata-enterprise</sonar.organization>
        <sonar.coverage.exclusions>
            tm-enterprise/**/dto/*,
            tm-enterprise/**/entity/*,
            tm-enterprise/**/vo/*,
            tm-enterprise/**/controller/*,
            tm-enterprise/**/constant/*,
            **/*Dto.java,
            **/*Entity.java,
            **/*Enum.java,
            iengine/iengine-common/src/main/java/com/tapdata/entity/*,
            coverage/lcov-report/*,
            **/*Exception.java
        </sonar.coverage.exclusions>
        <sonar.exclusions>
            manager/tm/**/dto/*,
            manager/tm/**/entity/*,
            manager/tm/**/vo/*,
            manager/tm/**/controller/*,
            manager/tm/**/constant/*,
            **/*Dto.java,
            **/*Enum.java,
            iengine/iengine-common/src/main/java/com/tapdata/entity/*,
            coverage/lcov-report/*,
            **/*Exception.java
        </sonar.exclusions>
        <sonar.test.exclusions>
            **/src/test/**
        </sonar.test.exclusions>

        <junit-jupiter.version>5.8.1</junit-jupiter.version>
        <modules-api.version>2.1-SNAPSHOT</modules-api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit-jupiter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0</version>
                    <configuration>
                        <argLine>
                            --add-opens=java.base/java.lang=ALL-UNNAMED
                            --add-opens=java.base/java.util=ALL-UNNAMED
                            --add-opens=java.base/java.security=ALL-UNNAMED
                            --add-opens=java.base/sun.security.rsa=ALL-UNNAMED
                            --add-opens=java.base/sun.security.x509=ALL-UNNAMED
                            --add-opens=java.base/sun.security.util=ALL-UNNAMED
                            --add-opens=java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                            -XX:+UnlockExperimentalVMOptions --add-exports=java.base/jdk.internal.ref=ALL-UNNAMED
                            --add-exports=java.base/sun.nio.ch=ALL-UNNAMED
                            --add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED
                            --add-exports=jdk.compiler/com.sun.tools.javac.file=ALL-UNNAMED
                            --add-opens=jdk.compiler/com.sun.tools.javac=ALL-UNNAMED
                            --add-opens=java.base/java.lang.reflect=ALL-UNNAMED
                            --add-opens=java.base/java.io=ALL-UNNAMED
                            --add-opens=java.base/java.util=ALL-UNNAMED
                            --add-modules=java.se
                            --add-opens=java.management/sun.management=ALL-UNNAMED
                            --add-opens=jdk.management/com.sun.management.internal=ALL-UNNAMED
                            --add-opens=java.base/jdk.internal.loader=ALL-UNNAMED
                            ${surefireArgLine}
                        </argLine>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                    <executions>
                        <!--first execution : for preparing JaCoCo runtime agent-->
                        <execution>
                            <id>prepare-agent</id>
                            <goals>
                                <goal>prepare-agent</goal>
                            </goals>
                            <configuration>
                                <propertyName>surefireArgLine</propertyName>
                            </configuration>
                        </execution>
                        <!--second execution : for creating code coverage reports-->
                        <execution>
                            <id>report</id>
                            <phase>test</phase>
                            <goals>
                                <goal>report</goal>
                            </goals>
                            <configuration>
                                <formats>XML</formats>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

</project>